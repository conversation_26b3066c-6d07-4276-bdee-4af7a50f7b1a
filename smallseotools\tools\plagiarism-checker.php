<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plagiarism Checker - Free Online Plagiarism Detection Tool</title>
    <meta name="description" content="Check for plagiarism in your content with our free online plagiarism checker. Detect duplicate content and ensure originality.">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 10px 0;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
        }
        
        .header-links {
            display: flex;
            gap: 20px;
        }
        
        .header-links a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        
        .header-links a:hover {
            color: #007bff;
        }
        
        /* Main Content */
        .main-content {
            background: #fff;
            margin: 20px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tool-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            outline: none;
        }
        
        .form-textarea:focus {
            border-color: #007bff;
        }
        
        .char-count {
            text-align: right;
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .result-title {
            font-size: 20px;
            margin-bottom: 15px;
            color: #333;
        }
        
        .plagiarism-score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .score-unique {
            color: #28a745;
        }
        
        .score-plagiarized {
            color: #dc3545;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 10px;
            }
            
            .main-content {
                margin: 10px 0;
                padding: 20px;
            }
            
            .tool-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <a href="../index.php" class="logo">Small SEO Tools</a>
                <div class="header-links">
                    <a href="../login.php">Login</a>
                    <a href="../pricing.php">Pricing</a>
                    <a href="../contact.php">Contact</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <h1 class="tool-title">Plagiarism Checker</h1>
            <p class="tool-description">
                Check your content for plagiarism with our advanced plagiarism detection tool. 
                Simply paste your text below and click "Check Plagiarism" to scan for duplicate content.
            </p>
            
            <form id="plagiarismForm">
                <div class="form-group">
                    <label for="textInput" class="form-label">Enter your text to check for plagiarism:</label>
                    <textarea 
                        id="textInput" 
                        class="form-textarea" 
                        placeholder="Paste your content here to check for plagiarism..."
                        maxlength="5000"
                    ></textarea>
                    <div class="char-count">
                        <span id="charCount">0</span> / 5000 characters
                    </div>
                </div>
                
                <button type="submit" class="btn" id="checkBtn">Check Plagiarism</button>
            </form>
            
            <div id="resultSection" class="result-section">
                <div id="loadingDiv" class="loading">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p>Checking for plagiarism... Please wait.</p>
                </div>
                
                <div id="resultDiv" style="display: none;">
                    <h3 class="result-title">Plagiarism Check Results</h3>
                    <div class="plagiarism-score" id="plagiarismScore"></div>
                    <div id="resultDetails"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Small SEO Tools. All rights reserved.</p>
        </div>
    </footer>

    <script>
        const textInput = document.getElementById('textInput');
        const charCount = document.getElementById('charCount');
        const plagiarismForm = document.getElementById('plagiarismForm');
        const checkBtn = document.getElementById('checkBtn');
        const resultSection = document.getElementById('resultSection');
        const loadingDiv = document.getElementById('loadingDiv');
        const resultDiv = document.getElementById('resultDiv');
        const progressFill = document.getElementById('progressFill');
        const plagiarismScore = document.getElementById('plagiarismScore');
        const resultDetails = document.getElementById('resultDetails');

        // Character counter
        textInput.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            if (count > 0) {
                checkBtn.disabled = false;
            } else {
                checkBtn.disabled = true;
            }
        });

        // Form submission
        plagiarismForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const text = textInput.value.trim();
            if (!text) {
                alert('Please enter some text to check for plagiarism.');
                return;
            }
            
            checkPlagiarism(text);
        });

        function checkPlagiarism(text) {
            resultSection.style.display = 'block';
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            checkBtn.disabled = true;
            
            // Simulate progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    setTimeout(showResults, 500);
                }
            }, 200);
        }

        function showResults() {
            loadingDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            checkBtn.disabled = false;
            
            // Simulate plagiarism check results
            const uniquePercentage = Math.floor(Math.random() * 30) + 70; // 70-100% unique
            const plagiarizedPercentage = 100 - uniquePercentage;
            
            if (plagiarizedPercentage === 0) {
                plagiarismScore.innerHTML = `<span class="score-unique">${uniquePercentage}% Unique Content</span>`;
                plagiarismScore.className = 'plagiarism-score score-unique';
                resultDetails.innerHTML = `
                    <p><strong>Great news!</strong> Your content appears to be completely original.</p>
                    <p>No plagiarism detected in the submitted text.</p>
                `;
            } else {
                plagiarismScore.innerHTML = `<span class="score-plagiarized">${plagiarizedPercentage}% Plagiarized</span> | <span class="score-unique">${uniquePercentage}% Unique</span>`;
                resultDetails.innerHTML = `
                    <p><strong>Plagiarism detected!</strong> ${plagiarizedPercentage}% of your content matches existing sources.</p>
                    <p>Please review and rewrite the flagged sections to ensure originality.</p>
                `;
            }
        }

        // Initialize
        checkBtn.disabled = true;
    </script>
</body>
</html>
