<?php
require_once '../config/config.php';

$page_title = 'Social Media Caption Generator - Free AI Caption Creator';
$page_description = 'Generate engaging social media captions for Instagram, Facebook, Twitter, and LinkedIn. AI-powered caption generator with hashtags and emojis.';
$page_keywords = 'social media caption generator, instagram caption generator, facebook caption, twitter caption, AI caption generator, social media content';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Social Media Caption Generator",
        "description": "Generate engaging social media captions for Instagram, Facebook, Twitter, and LinkedIn.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "Social Media Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "ToolsForge",
            "url": "<?php echo SITE_URL; ?>"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Social Media Caption Generator
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Create engaging captions for Instagram, Facebook, Twitter, and LinkedIn with AI-powered suggestions and relevant hashtags.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #EC4899;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #EC4899, #DB2777); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">AI-Powered Caption Creator</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Generate compelling captions that boost engagement across all social platforms</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Content Topic or Description:</label>
                                <textarea id="contentTopic" rows="4" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 resize-none font-medium text-sm lg:text-base" style="border-color: #FBCFE8; background: linear-gradient(45deg, #FDF2F8, #FBCFE8);" placeholder="Describe your post content, product, or topic...&#10;Example: New coffee shop opening, morning workout routine, travel photos from Paris"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Platform:</label>
                                <select id="platform" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #FBCFE8; background: linear-gradient(45deg, #FDF2F8, #FBCFE8);">
                                    <option value="instagram">Instagram</option>
                                    <option value="facebook">Facebook</option>
                                    <option value="twitter">Twitter</option>
                                    <option value="linkedin">LinkedIn</option>
                                    <option value="tiktok">TikTok</option>
                                    <option value="youtube">YouTube</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Tone:</label>
                                <select id="tone" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #FBCFE8; background: linear-gradient(45deg, #FDF2F8, #FBCFE8);">
                                    <option value="casual">Casual & Friendly</option>
                                    <option value="professional">Professional</option>
                                    <option value="funny">Funny & Humorous</option>
                                    <option value="inspirational">Inspirational</option>
                                    <option value="promotional">Promotional</option>
                                    <option value="educational">Educational</option>
                                    <option value="emotional">Emotional</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Caption Length:</label>
                                <select id="captionLength" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #FBCFE8; background: linear-gradient(45deg, #FDF2F8, #FBCFE8);">
                                    <option value="short">Short (1-2 sentences)</option>
                                    <option value="medium">Medium (3-5 sentences)</option>
                                    <option value="long">Long (6+ sentences)</option>
                                </select>
                            </div>

                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="includeHashtags" checked class="rounded border-gray-300 text-pink-600 focus:ring-pink-500">
                                    <span class="ml-2 text-gray-700 font-medium">Include Hashtags</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="includeEmojis" checked class="rounded border-gray-300 text-pink-600 focus:ring-pink-500">
                                    <span class="ml-2 text-gray-700 font-medium">Include Emojis</span>
                                </label>
                            </div>

                            <button onclick="generateCaptions()" class="w-full px-6 py-3 lg:py-4 text-white font-bold text-base lg:text-lg rounded-lg lg:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(45deg, #EC4899, #DB2777);">
                                <span class="flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span>Generate Captions</span>
                                </span>
                            </button>
                        </div>

                        <!-- Results Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Generated Captions:</label>
                                <div id="captionsContainer" class="w-full min-h-[500px] px-3 lg:px-4 py-3 lg:py-4 border-2 rounded-lg lg:rounded-xl overflow-y-auto" style="border-color: #FBCFE8; background: linear-gradient(45deg, #FDF2F8, #FBCFE8);">
                                    <div class="text-center text-gray-500 py-8">
                                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <p class="text-lg font-medium">Enter your content topic to generate captions</p>
                                        <p class="text-sm mt-2">Multiple caption variations will appear here</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-4" style="background: linear-gradient(45deg, #EC4899, #8B5CF6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Boost Your Social Media Engagement</h2>
                    <p class="text-gray-600 text-lg max-w-3xl mx-auto">Create compelling captions that drive likes, comments, and shares across all social platforms</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6 bg-pink-50 rounded-xl">
                        <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">AI-Powered</h3>
                        <p class="text-gray-600">Advanced AI generates unique, engaging captions tailored to your content</p>
                    </div>

                    <div class="text-center p-6 bg-purple-50 rounded-xl">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Smart Hashtags</h3>
                        <p class="text-gray-600">Automatically includes relevant hashtags to increase discoverability</p>
                    </div>

                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Multi-Platform</h3>
                        <p class="text-gray-600">Optimized for Instagram, Facebook, Twitter, LinkedIn, and more</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/social-media-caption-generator.js"></script>
</body>
</html>
