// ToolsForge Search Functionality
class ToolsSearch {
    constructor() {
        this.tools = [
            // SEO Tools
            { name: 'Plagiarism Checker', url: 'tools/plagiarism-checker.php', category: 'Writing', description: 'Check for duplicate content and ensure originality' },
            { name: 'Grammar Checker', url: 'tools/grammar-checker.php', category: 'Writing', description: 'Fix grammar and spelling errors instantly' },
            { name: 'Paraphrasing Tool', url: 'tools/paraphrasing-tool.php', category: 'Writing', description: 'Rewrite content while maintaining meaning' },
            { name: 'Backlink Checker', url: 'tools/backlink-checker.php', category: 'SEO', description: 'Analyze your website\'s backlink profile' },
            { name: 'Domain Authority Checker', url: 'tools/domain-authority-checker.php', category: 'SEO', description: 'Check domain authority and SEO metrics' },
            { name: 'Keyword Research Tool', url: 'tools/keyword-research-tool.php', category: 'SEO', description: 'Research and analyze keywords for SEO' },
            { name: 'Keyword Position Checker', url: 'tools/keyword-position-checker.php', category: 'SEO', description: 'Check keyword rankings in search results' },
            { name: 'Meta Tag Generator', url: 'tools/meta-tag-generator.php', category: 'SEO', description: 'Generate SEO-optimized meta tags' },
            { name: 'Sitemap Generator', url: 'tools/sitemap-generator.php', category: 'SEO', description: 'Generate XML sitemaps for better indexing' },
            { name: 'Robots.txt Generator', url: 'tools/robots-txt-generator.php', category: 'SEO', description: 'Generate robots.txt file for search engines' },
            { name: 'Schema Markup Generator', url: 'tools/schema-markup-generator.php', category: 'SEO', description: 'Generate structured data markup' },
            { name: 'Canonical URL Generator', url: 'tools/canonical-url-generator.php', category: 'SEO', description: 'Generate canonical URLs to prevent duplicate content' },
            { name: 'Open Graph Generator', url: 'tools/open-graph-generator.php', category: 'SEO', description: 'Generate Open Graph meta tags for social media' },
            { name: 'Twitter Card Generator', url: 'tools/twitter-card-generator.php', category: 'SEO', description: 'Generate Twitter Card meta tags' },
            { name: 'SERP Checker', url: 'tools/serp-checker.php', category: 'SEO', description: 'Check search engine rankings' },
            { name: 'Website Analyzer', url: 'tools/website-analyzer.php', category: 'SEO', description: 'Comprehensive website SEO analysis' },
            { name: 'Page Speed Analyzer', url: 'tools/page-speed-analyzer.php', category: 'SEO', description: 'Analyze website loading speed' },
            { name: 'SSL Checker', url: 'tools/ssl-checker.php', category: 'SEO', description: 'Check SSL certificate status' },
            { name: 'Broken Link Checker', url: 'tools/broken-link-checker.php', category: 'SEO', description: 'Find and fix broken links' },
            
            // Writing Tools
            { name: 'Word Counter', url: 'tools/word-counter.php', category: 'Writing', description: 'Count words, characters, and paragraphs' },
            { name: 'Character Counter', url: 'tools/character-counter.php', category: 'Writing', description: 'Count characters in your text' },
            { name: 'Article Rewriter', url: 'tools/article-rewriter.php', category: 'Writing', description: 'Rewrite articles to make them unique' },
            { name: 'Text Summarizer', url: 'tools/text-summarizer.php', category: 'Writing', description: 'Summarize long text content' },
            { name: 'Readability Checker', url: 'tools/readability-checker.php', category: 'Writing', description: 'Check content readability score' },
            { name: 'Case Converter', url: 'tools/case-converter.php', category: 'Writing', description: 'Convert text case (upper, lower, title)' },
            { name: 'Lorem Ipsum Generator', url: 'tools/lorem-ipsum-generator.php', category: 'Writing', description: 'Generate placeholder text' },
            { name: 'Blog Idea Generator', url: 'tools/blog-idea-generator.php', category: 'Writing', description: 'Generate creative blog post ideas' },
            { name: 'Content Optimizer', url: 'tools/content-optimizer.php', category: 'Writing', description: 'Optimize content for better SEO' },
            { name: 'Keyword Density Checker', url: 'tools/keyword-density-checker.php', category: 'Writing', description: 'Check keyword density in content' },
            { name: 'Title Case Converter', url: 'tools/title-case-converter.php', category: 'Writing', description: 'Convert text to title case' },
            
            // Image Tools
            { name: 'Image Compressor', url: 'tools/image-compressor.php', category: 'Image', description: 'Compress images without losing quality' },
            { name: 'Image Resizer', url: 'tools/image-resizer.php', category: 'Image', description: 'Resize images to specific dimensions' },
            { name: 'Image Optimizer', url: 'tools/image-optimizer.php', category: 'Image', description: 'Optimize images for web performance' },
            { name: 'Favicon Generator', url: 'tools/favicon-generator.php', category: 'Image', description: 'Generate favicons in multiple sizes' },
            { name: 'Logo Maker', url: 'tools/logo-maker.php', category: 'Image', description: 'Create professional logos' },
            { name: 'Alt Text Generator', url: 'tools/alt-text-generator.php', category: 'Image', description: 'Generate SEO-friendly alt text for images' },
            { name: 'QR Code Generator', url: 'tools/qr-code-generator.php', category: 'Utility', description: 'Generate QR codes for URLs and text' },
            
            // Developer Tools
            { name: 'HTML Minifier', url: 'tools/html-minifier.php', category: 'Developer', description: 'Minify HTML code' },
            { name: 'CSS Minifier', url: 'tools/css-minifier.php', category: 'Developer', description: 'Minify CSS code' },
            { name: 'JavaScript Minifier', url: 'tools/javascript-minifier.php', category: 'Developer', description: 'Minify JavaScript code' },
            { name: 'JSON Formatter', url: 'tools/json-formatter.php', category: 'Developer', description: 'Format and validate JSON' },
            { name: 'XML Formatter', url: 'tools/xml-formatter.php', category: 'Developer', description: 'Format and validate XML' },
            { name: 'Code Formatter', url: 'tools/code-formatter.php', category: 'Developer', description: 'Format various code types' },
            { name: 'Base64 Encoder/Decoder', url: 'tools/base64-encoder-decoder.php', category: 'Developer', description: 'Encode and decode Base64 strings' },
            { name: 'URL Encoder/Decoder', url: 'tools/url-encoder-decoder.php', category: 'Developer', description: 'Encode and decode URLs' },
            { name: 'HTML Encoder/Decoder', url: 'tools/html-encoder-decoder.php', category: 'Developer', description: 'Encode and decode HTML entities' },
            { name: 'Hash Generator', url: 'tools/hash-generator.php', category: 'Developer', description: 'Generate MD5, SHA1, SHA256 hashes' },
            { name: 'Password Generator', url: 'tools/password-generator.php', category: 'Utility', description: 'Generate secure passwords' },
            
            // Utility Tools
            { name: 'Color Picker', url: 'tools/color-picker.php', category: 'Utility', description: 'Pick colors and generate palettes' },
            { name: 'Email Validator', url: 'tools/email-validator.php', category: 'Utility', description: 'Validate email addresses' },
            { name: 'Link Shortener', url: 'tools/link-shortener.php', category: 'Utility', description: 'Shorten long URLs' },
            { name: 'UTM Builder', url: 'tools/utm-builder.php', category: 'Marketing', description: 'Build UTM tracking parameters' },
            { name: 'Email Signature Generator', url: 'tools/email-signature-generator.php', category: 'Utility', description: 'Create professional email signatures' },
            { name: 'Fake Name Generator', url: 'tools/fake-name-generator.php', category: 'Utility', description: 'Generate fake names for testing' },
            { name: 'Hashtag Generator', url: 'tools/hashtag-generator.php', category: 'Social Media', description: 'Generate relevant hashtags' },
            { name: 'Instagram Caption Generator', url: 'tools/instagram-caption-generator.php', category: 'Social Media', description: 'Generate Instagram captions' },
            { name: 'Social Media Caption Generator', url: 'tools/social-media-caption-generator.php', category: 'Social Media', description: 'AI-powered caption generator for all social platforms' },
            { name: 'YouTube Tag Generator', url: 'tools/youtube-tag-generator.php', category: 'Social Media', description: 'Generate YouTube video tags' },
            { name: 'YouTube Thumbnail Downloader', url: 'tools/youtube-thumbnail-downloader.php', category: 'Social Media', description: 'Download YouTube video thumbnails in HD quality' },
            
            // PDF Tools
            { name: 'PDF Tools', url: 'tools/pdf-tools.php', category: 'PDF', description: 'Various PDF manipulation tools' },
            { name: 'PDF Merger', url: 'tools/pdf-merger.php', category: 'PDF', description: 'Merge multiple PDF files' },
            
            // Converter Tools
            { name: 'Text to HTML Converter', url: 'tools/text-to-html-converter.php', category: 'Converter', description: 'Convert text to HTML format' },
            { name: 'Markdown to HTML Converter', url: 'tools/markdown-to-html-converter.php', category: 'Converter', description: 'Convert Markdown to HTML' },
            { name: 'HTML Tag Stripper', url: 'tools/html-tag-stripper.php', category: 'Converter', description: 'Remove HTML tags from text' },
            { name: 'Slug Generator', url: 'tools/slug-generator.php', category: 'Converter', description: 'Generate URL-friendly slugs' }
        ];
        
        this.searchInput = document.getElementById('toolSearch');
        this.searchResults = document.getElementById('searchResults');
        this.init();
    }

    init() {
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
            this.searchInput.addEventListener('focus', () => this.showPopularTools());
            
            // Close search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.searchInput.contains(e.target) && !this.searchResults.contains(e.target)) {
                    this.hideResults();
                }
            });
        }
    }

    handleSearch(e) {
        const query = e.target.value.trim().toLowerCase();
        
        if (query.length === 0) {
            this.showPopularTools();
            return;
        }

        if (query.length < 2) {
            this.hideResults();
            return;
        }

        const results = this.tools.filter(tool => 
            tool.name.toLowerCase().includes(query) ||
            tool.description.toLowerCase().includes(query) ||
            tool.category.toLowerCase().includes(query)
        );

        this.displayResults(results, query);
    }

    showPopularTools() {
        const popularTools = [
            'Plagiarism Checker',
            'Grammar Checker', 
            'Paraphrasing Tool',
            'Image Compressor',
            'Backlink Checker',
            'Domain Authority Checker',
            'Word Counter',
            'Meta Tag Generator'
        ];

        const popular = this.tools.filter(tool => popularTools.includes(tool.name));
        this.displayResults(popular, '', 'Popular Tools');
    }

    displayResults(results, query = '', title = '') {
        if (results.length === 0 && query) {
            this.searchResults.innerHTML = `
                <div class="p-4 text-center text-gray-500">
                    <p>No tools found for "${query}"</p>
                    <p class="text-sm mt-2">Try searching for: plagiarism, grammar, image, seo, backlink</p>
                </div>
            `;
            this.searchResults.classList.remove('hidden');
            return;
        }

        let html = '';
        
        if (title) {
            html += `<div class="px-4 py-2 bg-gray-50 border-b border-gray-200 font-semibold text-gray-700">${title}</div>`;
        }

        results.slice(0, 8).forEach(tool => {
            const highlightedName = query ? this.highlightText(tool.name, query) : tool.name;
            const highlightedDesc = query ? this.highlightText(tool.description, query) : tool.description;
            
            html += `
                <a href="${tool.url}" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800">${highlightedName}</div>
                            <div class="text-sm text-gray-600 mt-1">${highlightedDesc}</div>
                        </div>
                        <div class="ml-4">
                            <span class="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">${tool.category}</span>
                        </div>
                    </div>
                </a>
            `;
        });

        if (results.length > 8) {
            html += `
                <div class="px-4 py-3 bg-gray-50 text-center">
                    <a href="pages/tools.php" class="text-blue-600 font-semibold hover:text-blue-700">
                        View all ${results.length} results →
                    </a>
                </div>
            `;
        }

        this.searchResults.innerHTML = html;
        this.searchResults.classList.remove('hidden');
    }

    highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
    }

    hideResults() {
        this.searchResults.classList.add('hidden');
    }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ToolsSearch();
});

// Category filtering for tools page
class ToolsFilter {
    constructor() {
        this.init();
    }

    init() {
        // Add category filter functionality if on tools page
        if (window.location.pathname.includes('tools.php')) {
            this.setupCategoryFilters();
        }
    }

    setupCategoryFilters() {
        // This will be implemented when we enhance the tools page
        console.log('Tools filter initialized');
    }
}

// Initialize tools filter
document.addEventListener('DOMContentLoaded', () => {
    new ToolsFilter();
});
