// Keyword Position Checker Functionality
let rankingResults = [];

function checkKeywordPositions() {
    const websiteUrl = document.getElementById('websiteUrl').value.trim();
    const keywordsInput = document.getElementById('keywordsInput').value.trim();
    const searchEngine = document.getElementById('searchEngine').value;
    const topResults = parseInt(document.getElementById('topResults').value);
    
    // Validation
    if (!websiteUrl) {
        showError('Please enter a website URL');
        return;
    }
    
    if (!keywordsInput) {
        showError('Please enter at least one keyword');
        return;
    }
    
    // Validate URL format
    try {
        new URL(websiteUrl);
    } catch (e) {
        showError('Please enter a valid URL (e.g., https://example.com)');
        return;
    }
    
    const keywords = keywordsInput.split('\n').filter(k => k.trim()).map(k => k.trim());
    
    if (keywords.length === 0) {
        showError('Please enter at least one keyword');
        return;
    }
    
    // Clear previous results
    rankingResults = [];
    
    // Show loading state
    showLoading();
    
    // Process keywords
    processKeywords(websiteUrl, keywords, searchEngine, topResults);
}

function processKeywords(websiteUrl, keywords, searchEngine, topResults) {
    const resultsContainer = document.getElementById('resultsContainer');
    let processedCount = 0;
    
    // Extract domain from URL for comparison
    const domain = extractDomain(websiteUrl);
    
    resultsContainer.innerHTML = `
        <div class="space-y-4">
            <div class="text-center mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-2">Checking Rankings for ${domain}</h3>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">Processing <span id="currentKeyword">0</span> of ${keywords.length} keywords</p>
            </div>
            <div id="keywordResults" class="space-y-3">
                <!-- Results will be added here -->
            </div>
        </div>
    `;
    
    // Simulate checking each keyword (in a real implementation, this would make API calls)
    keywords.forEach((keyword, index) => {
        setTimeout(() => {
            const position = simulateKeywordCheck(domain, keyword, topResults);
            const result = {
                keyword: keyword,
                position: position,
                url: websiteUrl,
                searchEngine: searchEngine,
                topResults: topResults,
                timestamp: new Date().toISOString()
            };
            
            rankingResults.push(result);
            addKeywordResult(keyword, position, index + 1);
            
            processedCount++;
            updateProgress(processedCount, keywords.length);
            
            if (processedCount === keywords.length) {
                finishProcessing();
            }
        }, (index + 1) * 1000); // Stagger requests by 1 second
    });
}

function simulateKeywordCheck(domain, keyword, topResults) {
    // This is a simulation - in a real implementation, you would:
    // 1. Make API calls to search engines or SERP APIs
    // 2. Parse search results
    // 3. Find the domain's position
    
    // For demo purposes, generate realistic-looking results
    const random = Math.random();
    
    if (random < 0.3) {
        // 30% chance of ranking in top 10
        return Math.floor(Math.random() * 10) + 1;
    } else if (random < 0.5) {
        // 20% chance of ranking in 11-50
        return Math.floor(Math.random() * 40) + 11;
    } else if (random < 0.7) {
        // 20% chance of ranking in 51-100
        return Math.floor(Math.random() * 50) + 51;
    } else {
        // 30% chance of not ranking in top results
        return null;
    }
}

function addKeywordResult(keyword, position, index) {
    const keywordResults = document.getElementById('keywordResults');
    const positionText = position ? `#${position}` : 'Not in top results';
    const positionClass = position ? (position <= 10 ? 'text-green-600' : position <= 50 ? 'text-yellow-600' : 'text-orange-600') : 'text-red-600';
    const bgClass = position ? (position <= 10 ? 'bg-green-50' : position <= 50 ? 'bg-yellow-50' : 'bg-orange-50') : 'bg-red-50';
    
    const resultHtml = `
        <div class="flex items-center justify-between p-4 ${bgClass} rounded-lg border border-gray-200">
            <div class="flex-1">
                <div class="font-semibold text-gray-800">${escapeHtml(keyword)}</div>
                <div class="text-sm text-gray-600">Keyword ${index}</div>
            </div>
            <div class="text-right">
                <div class="font-bold ${positionClass} text-lg">${positionText}</div>
                ${position ? `<div class="text-xs text-gray-500">Page ${Math.ceil(position / 10)}</div>` : ''}
            </div>
        </div>
    `;
    
    keywordResults.insertAdjacentHTML('beforeend', resultHtml);
}

function updateProgress(current, total) {
    const progressBar = document.getElementById('progressBar');
    const currentKeyword = document.getElementById('currentKeyword');
    
    const percentage = (current / total) * 100;
    progressBar.style.width = `${percentage}%`;
    currentKeyword.textContent = current;
}

function finishProcessing() {
    // Enable export buttons
    document.getElementById('exportCsvBtn').disabled = false;
    document.getElementById('copyBtn').disabled = false;
    
    // Add summary
    const keywordResults = document.getElementById('keywordResults');
    const rankedCount = rankingResults.filter(r => r.position).length;
    const top10Count = rankingResults.filter(r => r.position && r.position <= 10).length;
    
    const summaryHtml = `
        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 class="font-bold text-blue-800 mb-2">Summary</h4>
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600">${rankingResults.length}</div>
                    <div class="text-sm text-blue-700">Keywords Checked</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600">${rankedCount}</div>
                    <div class="text-sm text-green-700">Keywords Ranking</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-600">${top10Count}</div>
                    <div class="text-sm text-yellow-700">Top 10 Rankings</div>
                </div>
            </div>
        </div>
    `;
    
    keywordResults.insertAdjacentHTML('beforeend', summaryHtml);
}

function showLoading() {
    const resultsContainer = document.getElementById('resultsContainer');
    resultsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-lg font-medium text-gray-700">Checking keyword positions...</p>
            <p class="text-sm text-gray-500 mt-2">This may take a few moments</p>
        </div>
    `;
}

function showError(message) {
    const resultsContainer = document.getElementById('resultsContainer');
    resultsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-lg font-medium text-red-700">${escapeHtml(message)}</p>
        </div>
    `;
}

function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname.replace('www.', '');
    } catch (e) {
        return url;
    }
}

function exportResults(format) {
    if (rankingResults.length === 0) {
        alert('No results to export');
        return;
    }
    
    if (format === 'csv') {
        const csvContent = generateCSV();
        downloadFile(csvContent, 'keyword-rankings.csv', 'text/csv');
    }
}

function generateCSV() {
    const headers = ['Keyword', 'Position', 'Page', 'URL', 'Search Engine', 'Date'];
    const rows = rankingResults.map(result => [
        result.keyword,
        result.position || 'Not Found',
        result.position ? Math.ceil(result.position / 10) : 'N/A',
        result.url,
        result.searchEngine,
        new Date(result.timestamp).toLocaleDateString()
    ]);
    
    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');
    
    return csvContent;
}

function copyResults() {
    if (rankingResults.length === 0) {
        alert('No results to copy');
        return;
    }
    
    const textContent = rankingResults.map(result => 
        `${result.keyword}: ${result.position ? `#${result.position}` : 'Not Found'}`
    ).join('\n');
    
    navigator.clipboard.writeText(textContent).then(() => {
        // Show success message
        const copyBtn = document.getElementById('copyBtn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<span class="flex items-center justify-center space-x-2"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span>Copied!</span></span>';
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
        }, 2000);
    }).catch(() => {
        alert('Failed to copy results to clipboard');
    });
}

function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for inputs
    document.getElementById('websiteUrl').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            checkKeywordPositions();
        }
    });
    
    document.getElementById('keywordsInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            checkKeywordPositions();
        }
    });
});
