<?php
require_once '../config/config.php';

$page_title = 'Website Speed Optimizer - Performance Analysis & Optimization Tips';
$page_description = 'Analyze your website speed and get actionable optimization recommendations. Improve Core Web Vitals, loading times, and user experience.';
$page_keywords = 'website speed optimizer, page speed test, performance analysis, core web vitals, website optimization, loading speed';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Website Speed Optimizer",
        "description": "Analyze your website speed and get actionable optimization recommendations.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "ToolsForge",
            "url": "<?php echo SITE_URL; ?>"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Website Speed Optimizer
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Analyze your website's performance and get actionable recommendations to improve loading speed and Core Web Vitals.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #3B82F6;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #3B82F6, #1D4ED8); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Performance Analysis Tool</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Test your website speed and get detailed optimization recommendations</p>
                    </div>

                    <!-- URL Input Section -->
                    <div class="max-w-2xl mx-auto mb-8">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <input type="url" id="websiteUrl" class="flex-1 px-4 py-3 border-2 rounded-xl focus:outline-none text-gray-800 font-medium text-lg" style="border-color: #DBEAFE; background: linear-gradient(45deg, #EFF6FF, #DBEAFE);" placeholder="https://example.com">
                            <button onclick="analyzeSpeed()" class="px-8 py-3 text-white font-bold text-lg rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(45deg, #3B82F6, #1D4ED8);">
                                <span class="flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span>Analyze Speed</span>
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Results Container -->
                    <div id="resultsContainer" class="hidden">
                        <!-- Performance Score -->
                        <div class="text-center mb-8">
                            <div class="inline-flex items-center justify-center w-32 h-32 rounded-full border-8 border-blue-200 bg-blue-50 mb-4">
                                <div class="text-center">
                                    <div id="performanceScore" class="text-3xl font-bold text-blue-600">--</div>
                                    <div class="text-sm text-blue-700 font-medium">Performance</div>
                                </div>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-2">Speed Analysis Complete</h3>
                            <p id="analyzedUrl" class="text-gray-600"></p>
                        </div>

                        <!-- Core Web Vitals -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <!-- LCP -->
                            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
                                <div class="text-center">
                                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div id="lcpScore" class="text-2xl font-bold text-green-600 mb-1">--</div>
                                    <div class="text-sm font-medium text-gray-700">LCP (Largest Contentful Paint)</div>
                                </div>
                            </div>

                            <!-- FID -->
                            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
                                <div class="text-center">
                                    <div class="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                                        </svg>
                                    </div>
                                    <div id="fidScore" class="text-2xl font-bold text-yellow-600 mb-1">--</div>
                                    <div class="text-sm font-medium text-gray-700">FID (First Input Delay)</div>
                                </div>
                            </div>

                            <!-- CLS -->
                            <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
                                <div class="text-center">
                                    <div class="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                        </svg>
                                    </div>
                                    <div id="clsScore" class="text-2xl font-bold text-orange-600 mb-1">--</div>
                                    <div class="text-sm font-medium text-gray-700">CLS (Cumulative Layout Shift)</div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Metrics -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-bold text-gray-800">Detailed Performance Metrics</h3>
                            </div>
                            <div class="p-6">
                                <div id="detailedMetrics" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Metrics will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Optimization Recommendations -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-bold text-gray-800">Optimization Recommendations</h3>
                            </div>
                            <div class="p-6">
                                <div id="recommendations" class="space-y-4">
                                    <!-- Recommendations will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="loadingState" class="hidden text-center py-12">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-6"></div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Analyzing Website Speed...</h3>
                        <p class="text-gray-600">This may take a few moments while we test your website performance</p>
                        <div class="mt-6 max-w-md mx-auto">
                            <div class="flex justify-between text-sm text-gray-500 mb-2">
                                <span>Progress</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Initial State -->
                    <div id="initialState" class="text-center py-12">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to Test Speed</h3>
                        <p class="text-gray-600 max-w-2xl mx-auto">Enter your website URL above to get a comprehensive speed analysis with Core Web Vitals and optimization recommendations.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-4" style="background: linear-gradient(45deg, #3B82F6, #8B5CF6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Why Website Speed Matters</h2>
                    <p class="text-gray-600 text-lg max-w-3xl mx-auto">Fast websites provide better user experience, higher search rankings, and increased conversions</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Better SEO Rankings</h3>
                        <p class="text-gray-600">Google uses page speed as a ranking factor. Faster sites rank higher in search results.</p>
                    </div>

                    <div class="text-center p-6 bg-green-50 rounded-xl">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Higher Conversions</h3>
                        <p class="text-gray-600">A 1-second delay in page load time can reduce conversions by up to 7%.</p>
                    </div>

                    <div class="text-center p-6 bg-purple-50 rounded-xl">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Better User Experience</h3>
                        <p class="text-gray-600">Fast loading pages keep users engaged and reduce bounce rates significantly.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/website-speed-optimizer.js"></script>
</body>
</html>
