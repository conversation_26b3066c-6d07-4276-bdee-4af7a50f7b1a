<?php
require_once '../config/config.php';

$page_title = 'Keyword Position Checker - Free SERP Ranking Tool';
$page_description = 'Check keyword rankings in Google search results. Track your website position for target keywords. Free SERP checker with accurate ranking data.';
$page_keywords = 'keyword position checker, SERP checker, keyword ranking tool, google ranking checker, search position tracker, SEO ranking tool';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Keyword Position Checker",
        "description": "Check keyword rankings in Google search results. Track your website position for target keywords.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "ToolsForge",
            "url": "<?php echo SITE_URL; ?>"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Keyword Position Checker
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Track your website's keyword rankings in Google search results. Monitor SERP positions and improve your SEO strategy.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #3B82F6;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">SERP Position Tracker</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Check where your website ranks for specific keywords in Google search results</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Website URL:</label>
                                <input type="url" id="websiteUrl" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #DBEAFE; background: linear-gradient(45deg, #F0F9FF, #DBEAFE);" placeholder="https://example.com">
                            </div>
                            
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Keywords (one per line):</label>
                                <textarea id="keywordsInput" rows="8" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 resize-none font-medium text-sm lg:text-base" style="border-color: #DBEAFE; background: linear-gradient(45deg, #F0F9FF, #DBEAFE);" placeholder="Enter keywords to check rankings for:&#10;SEO tools&#10;keyword research&#10;backlink checker&#10;domain authority"></textarea>
                            </div>

                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Search Engine:</label>
                                <select id="searchEngine" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #DBEAFE; background: linear-gradient(45deg, #F0F9FF, #DBEAFE);">
                                    <option value="google.com">Google.com (Global)</option>
                                    <option value="google.co.uk">Google.co.uk (UK)</option>
                                    <option value="google.ca">Google.ca (Canada)</option>
                                    <option value="google.com.au">Google.com.au (Australia)</option>
                                    <option value="google.de">Google.de (Germany)</option>
                                    <option value="google.fr">Google.fr (France)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Check Top Results:</label>
                                <select id="topResults" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #DBEAFE; background: linear-gradient(45deg, #F0F9FF, #DBEAFE);">
                                    <option value="10">Top 10 Results</option>
                                    <option value="20">Top 20 Results</option>
                                    <option value="50" selected>Top 50 Results</option>
                                    <option value="100">Top 100 Results</option>
                                </select>
                            </div>

                            <button onclick="checkKeywordPositions()" class="w-full px-6 py-3 lg:py-4 text-white font-bold text-base lg:text-lg rounded-lg lg:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                <span class="flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <span>Check Keyword Positions</span>
                                </span>
                            </button>
                        </div>

                        <!-- Results Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Ranking Results:</label>
                                <div id="resultsContainer" class="w-full min-h-[400px] px-3 lg:px-4 py-3 lg:py-4 border-2 rounded-lg lg:rounded-xl text-gray-800 font-medium text-sm lg:text-base overflow-y-auto" style="border-color: #DBEAFE; background: linear-gradient(45deg, #F0F9FF, #DBEAFE);">
                                    <div class="text-center text-gray-500 py-8">
                                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        <p class="text-lg font-medium">Enter your website URL and keywords to check rankings</p>
                                        <p class="text-sm mt-2">Results will appear here after checking positions</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Export Options -->
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button onclick="exportResults('csv')" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled id="exportCsvBtn">
                                    <span class="flex items-center justify-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span>Export CSV</span>
                                    </span>
                                </button>
                                <button onclick="copyResults()" class="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg font-semibold hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled id="copyBtn">
                                    <span class="flex items-center justify-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>Copy Results</span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-4" style="background: linear-gradient(45deg, #3B82F6, #EC4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Track Your SEO Performance</h2>
                    <p class="text-gray-600 text-lg max-w-3xl mx-auto">Monitor keyword rankings and optimize your SEO strategy with accurate position data</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Accurate Rankings</h3>
                        <p class="text-gray-600">Get precise keyword position data from Google search results</p>
                    </div>

                    <div class="text-center p-6 bg-green-50 rounded-xl">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Multiple Locations</h3>
                        <p class="text-gray-600">Check rankings across different Google domains and regions</p>
                    </div>

                    <div class="text-center p-6 bg-purple-50 rounded-xl">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Export Data</h3>
                        <p class="text-gray-600">Export ranking results to CSV for further analysis and reporting</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/keyword-position-checker.js"></script>
</body>
</html>
