// Social Media Caption Generator Functionality

// Caption templates and patterns
const captionTemplates = {
    casual: {
        short: [
            "Just {topic}! 😊 What do you think?",
            "{topic} vibes today! ✨",
            "Loving this {topic} moment! 💕",
            "Can't get enough of {topic}! 🔥"
        ],
        medium: [
            "Had the most amazing time with {topic} today! 😍 There's something so special about moments like these that just make everything feel right. What's been making you smile lately?",
            "Sharing some {topic} love with you all! ✨ Life is all about these little moments that bring us joy. Hope this brightens your day as much as it did mine! 💫",
            "Just experienced the best {topic} ever! 🌟 Sometimes the simplest things bring the greatest happiness. Grateful for these beautiful moments in life. What are you grateful for today?"
        ],
        long: [
            "I can't even begin to describe how incredible this {topic} experience has been! 😍 From the moment I started, I knew this was going to be something special. There's just something magical about {topic} that fills my heart with so much joy and excitement. I'm so grateful to be able to share these moments with all of you amazing people! ✨ Life is truly beautiful when we take the time to appreciate the little things. What's been bringing you joy lately? I'd love to hear about your favorite {topic} moments! 💕"
        ]
    },
    professional: {
        short: [
            "Excited to share our latest {topic} insights.",
            "Proud to present our {topic} achievements.",
            "Introducing our innovative {topic} solution.",
            "Celebrating excellence in {topic}."
        ],
        medium: [
            "We're thrilled to showcase our latest {topic} developments. Our team has worked tirelessly to deliver exceptional results that exceed expectations. Thank you for your continued trust and support.",
            "Today marks a significant milestone in our {topic} journey. We're committed to innovation and excellence, and this achievement reflects our dedication to serving our valued clients and community.",
            "Proud to announce our latest {topic} initiative. This represents our ongoing commitment to quality, innovation, and customer satisfaction. We look forward to continuing this journey with you."
        ],
        long: [
            "We are incredibly proud to share our latest {topic} accomplishments with our valued community. This achievement represents months of dedicated work, innovative thinking, and collaborative effort from our exceptional team. Our commitment to excellence in {topic} drives everything we do, and we're grateful for the trust you place in us. As we continue to grow and evolve, we remain focused on delivering outstanding value and exceeding your expectations. Thank you for being part of our journey, and we look forward to sharing more exciting developments with you soon."
        ]
    },
    funny: {
        short: [
            "Me trying to understand {topic} 😂",
            "{topic} got me like... 🤪",
            "When {topic} hits different 😅",
            "Plot twist: {topic} happened! 🤣"
        ],
        medium: [
            "So apparently I thought I knew everything about {topic}... I was wrong! 😂 Life has a funny way of humbling us when we least expect it. Anyone else relate to this struggle? 🤷‍♀️",
            "Today's mood: completely obsessed with {topic} 🤪 My friends think I've lost it, but honestly, I'm just living my best life! Sometimes you gotta embrace the chaos, right? 😅",
            "Breaking news: Local person discovers {topic} and immediately becomes an expert! 📰 (Spoiler alert: I have no idea what I'm doing, but I'm having fun!) 🤣"
        ],
        long: [
            "Okay, so funny story about {topic}... 😂 I thought I had everything figured out, walked in with all the confidence in the world, and then reality hit me like a truck! 🚛 Turns out, {topic} is way more complicated than I imagined. But you know what? I'm embracing the chaos and laughing at myself because life's too short to take everything seriously! 🤪 Anyone else out there pretending to be an adult while secretly having no clue what they're doing? Asking for a friend... (that friend is me) 🙋‍♀️ #AdultingIsHard #FakeItTillYouMakeIt"
        ]
    },
    inspirational: {
        short: [
            "Every {topic} is a new beginning ✨",
            "Believe in your {topic} journey 🌟",
            "Your {topic} story matters 💫",
            "Embrace the {topic} within you 🌈"
        ],
        medium: [
            "Remember that every {topic} journey starts with a single step. 🌟 You have the power to create the life you've always dreamed of. Trust the process, embrace the challenges, and celebrate every small victory along the way. ✨",
            "Your {topic} story is unique and beautiful. 💫 Don't compare your chapter 1 to someone else's chapter 20. Focus on your growth, your progress, and your dreams. You're exactly where you need to be. 🌈",
            "Today is a reminder that {topic} can transform lives. 🌟 When we believe in ourselves and take action towards our goals, magic happens. Keep pushing forward, keep believing, and never give up on your dreams. ✨"
        ],
        long: [
            "I want to take a moment to remind you that your {topic} journey is absolutely incredible, even when it doesn't feel like it. 🌟 Every challenge you face is shaping you into the person you're meant to become. Every setback is setting you up for a comeback. Every small step forward is progress worth celebrating. ✨ You have within you right now everything you need to create the life you've always dreamed of. Trust yourself, trust the process, and remember that the most beautiful flowers grow through the dirt. 🌸 Your story matters, your dreams are valid, and your potential is limitless. Keep shining, beautiful soul! 💫 #BelieveInYourself #YouGotThis #DreamBig"
        ]
    },
    promotional: {
        short: [
            "Don't miss out on our {topic} offer! 🔥",
            "Limited time {topic} deal available now!",
            "Get your {topic} today - while supplies last!",
            "Exclusive {topic} promotion just for you! ✨"
        ],
        medium: [
            "🚨 SPECIAL OFFER ALERT! 🚨 Our amazing {topic} is now available at an incredible price! This is your chance to experience quality like never before. Don't wait - this offer won't last long! ⏰",
            "We're excited to bring you this exclusive {topic} promotion! 🎉 For a limited time only, you can enjoy premium quality at an unbeatable price. Join thousands of satisfied customers who have already discovered the difference! ✨",
            "Ready to transform your experience with {topic}? 🌟 This limited-time offer gives you access to our best-selling product at an amazing price. Don't miss out on this opportunity to upgrade your life! 🔥"
        ],
        long: [
            "🎉 INCREDIBLE NEWS! We're thrilled to announce our biggest {topic} promotion of the year! For a very limited time, you can get access to our premium {topic} solution at an unbeatable price. This isn't just any ordinary offer - this is your chance to experience the quality and results that thousands of our customers rave about every single day! ⭐ Our {topic} has been carefully crafted with your needs in mind, and we're confident you'll love the results. But hurry - this exclusive deal is only available while supplies last, and we don't want you to miss out! Click the link in our bio to secure your {topic} today and join our community of satisfied customers. Your future self will thank you! 🚀 #LimitedTime #ExclusiveOffer #DontMissOut"
        ]
    },
    educational: {
        short: [
            "Did you know? {topic} can change everything!",
            "Quick {topic} tip for your day 💡",
            "Learning something new about {topic} today!",
            "Here's what I discovered about {topic}..."
        ],
        medium: [
            "Today I want to share some valuable insights about {topic} that could make a real difference in your life. 📚 Understanding these key concepts can help you make better decisions and achieve better results. Knowledge is power! 💡",
            "Let's dive into the fascinating world of {topic}! 🌟 There are so many misconceptions out there, but the truth is actually quite simple once you understand the fundamentals. Here's what you need to know... 📖",
            "I've been researching {topic} extensively, and I'm excited to share what I've learned with you! 🔍 These insights have completely changed my perspective and I think they could help you too. Education is the key to growth! 🌱"
        ],
        long: [
            "I'm passionate about sharing knowledge, and today I want to educate you about something incredibly important: {topic}. 📚 Many people don't realize how much impact this can have on their daily lives, but once you understand the science and research behind it, everything starts to make sense. 🧠 The key is to approach {topic} with an open mind and a willingness to learn. I've spent countless hours studying this subject, consulting with experts, and testing different approaches. What I've discovered is that small changes in how we think about {topic} can lead to massive improvements in our results. 🌟 I encourage you to do your own research, ask questions, and never stop learning. Knowledge truly is power, and the more we understand about {topic}, the better equipped we are to make informed decisions that positively impact our lives. 💡 What questions do you have about {topic}? I'd love to continue this conversation in the comments! #Education #Knowledge #NeverStopLearning"
        ]
    },
    emotional: {
        short: [
            "This {topic} moment touched my heart 💕",
            "Feeling so grateful for {topic} today 🙏",
            "My heart is full because of {topic} ❤️",
            "Sometimes {topic} brings tears of joy 😭✨"
        ],
        medium: [
            "I'm sitting here with tears in my eyes thinking about {topic} and how much it has changed my life. 💕 There are moments that touch your soul so deeply, and this is one of them. Grateful beyond words for this beautiful experience. 🙏",
            "My heart is overflowing with emotion today because of {topic}. ❤️ Sometimes life gives us these precious gifts that remind us what truly matters. I'm so thankful for this moment and wanted to share it with all of you. ✨",
            "I never expected {topic} to impact me this deeply, but here I am, completely moved by this experience. 😭💕 Life has a way of surprising us with moments of pure beauty and connection. Feeling so blessed right now. 🌟"
        ],
        long: [
            "I'm struggling to find the right words to express how deeply {topic} has touched my heart today. 💕 As I sit here reflecting on this incredible experience, I'm overwhelmed with gratitude and emotion. There are moments in life that change us forever, that remind us of what's truly important, and this is one of those moments. 😭✨ {topic} has taught me so much about love, resilience, and the beauty of human connection. It's shown me that even in our darkest moments, there is always light to be found. I'm sharing this with you because I believe we all need reminders of the good in this world, the magic that exists in everyday moments, and the power we have to make a difference in each other's lives. 🌟 Thank you for being part of this journey with me. Your support and love mean everything. ❤️ #Grateful #Blessed #HeartFull #EmotionalMoment"
        ]
    }
};

// Hashtag collections by topic and platform
const hashtagCollections = {
    general: ['#love', '#instagood', '#photooftheday', '#beautiful', '#happy', '#follow', '#picoftheday', '#like4like', '#instadaily', '#amazing'],
    business: ['#business', '#entrepreneur', '#success', '#motivation', '#leadership', '#innovation', '#growth', '#strategy', '#professional', '#networking'],
    lifestyle: ['#lifestyle', '#life', '#inspiration', '#wellness', '#selfcare', '#mindfulness', '#positivity', '#goals', '#dreams', '#journey'],
    food: ['#food', '#foodie', '#delicious', '#yummy', '#cooking', '#recipe', '#foodporn', '#instafood', '#tasty', '#homemade'],
    travel: ['#travel', '#wanderlust', '#adventure', '#explore', '#vacation', '#trip', '#traveling', '#instatravel', '#tourism', '#journey'],
    fitness: ['#fitness', '#workout', '#gym', '#health', '#fit', '#training', '#exercise', '#motivation', '#strength', '#wellness'],
    fashion: ['#fashion', '#style', '#outfit', '#ootd', '#fashionista', '#trendy', '#stylish', '#look', '#clothing', '#accessories'],
    technology: ['#technology', '#tech', '#innovation', '#digital', '#software', '#coding', '#programming', '#ai', '#future', '#development']
};

// Emoji collections
const emojiCollections = {
    positive: ['😊', '😍', '🥰', '😘', '🤗', '😎', '🤩', '🙌', '👏', '💕', '❤️', '💖', '✨', '🌟', '⭐', '🔥', '💯', '🎉', '🎊', '🌈'],
    business: ['💼', '📈', '💰', '🏆', '🎯', '💡', '🚀', '⚡', '🔝', '📊', '💪', '🌟', '✅', '🎉', '🔥', '💯'],
    lifestyle: ['🌸', '🌺', '🌻', '🌷', '🍃', '🌿', '☀️', '🌙', '⭐', '✨', '💫', '🦋', '🌈', '💕', '❤️', '🙏'],
    food: ['🍕', '🍔', '🍟', '🌮', '🍝', '🍜', '🍲', '🥗', '🍰', '🧁', '🍪', '☕', '🍷', '😋', '🤤', '👨‍🍳'],
    travel: ['✈️', '🌍', '🗺️', '🏖️', '🏔️', '🏝️', '🚗', '🚢', '🎒', '📸', '🌅', '🌄', '🗽', '🏛️', '🎡', '🎢'],
    fitness: ['💪', '🏋️‍♀️', '🏃‍♂️', '🚴‍♀️', '🧘‍♀️', '⚽', '🏀', '🎾', '🏊‍♀️', '🔥', '💯', '🎯', '⚡', '🌟'],
    fashion: ['👗', '👠', '👜', '💄', '💅', '👑', '💎', '✨', '🌟', '💫', '👸', '💃', '🔥', '💯', '😍'],
    technology: ['💻', '📱', '⌚', '🖥️', '⌨️', '🖱️', '💾', '🔌', '🔋', '📡', '🛰️', '🚀', '⚡', '💡', '🔬']
};

function generateCaptions() {
    const contentTopic = document.getElementById('contentTopic').value.trim();
    const platform = document.getElementById('platform').value;
    const tone = document.getElementById('tone').value;
    const captionLength = document.getElementById('captionLength').value;
    const includeHashtags = document.getElementById('includeHashtags').checked;
    const includeEmojis = document.getElementById('includeEmojis').checked;
    
    if (!contentTopic) {
        showError('Please enter a content topic or description');
        return;
    }
    
    showLoading();
    
    // Simulate processing time
    setTimeout(() => {
        const captions = createCaptions(contentTopic, platform, tone, captionLength, includeHashtags, includeEmojis);
        displayCaptions(captions);
    }, 1500);
}

function createCaptions(topic, platform, tone, length, includeHashtags, includeEmojis) {
    const templates = captionTemplates[tone][length];
    const captions = [];
    
    // Generate 5 different caption variations
    for (let i = 0; i < 5; i++) {
        let caption = templates[i % templates.length].replace(/{topic}/g, topic);
        
        // Add emojis if requested
        if (includeEmojis) {
            caption = addEmojis(caption, topic, tone);
        }
        
        // Add hashtags if requested
        if (includeHashtags) {
            const hashtags = generateHashtags(topic, platform);
            caption += '\n\n' + hashtags;
        }
        
        // Add platform-specific optimizations
        caption = optimizeForPlatform(caption, platform);
        
        captions.push({
            text: caption,
            characterCount: caption.length,
            wordCount: caption.split(/\s+/).length
        });
    }
    
    return captions;
}

function addEmojis(caption, topic, tone) {
    const topicLower = topic.toLowerCase();
    let emojiSet = emojiCollections.positive; // default
    
    // Choose emoji set based on topic
    if (topicLower.includes('business') || topicLower.includes('work') || topicLower.includes('professional')) {
        emojiSet = emojiCollections.business;
    } else if (topicLower.includes('food') || topicLower.includes('cooking') || topicLower.includes('restaurant')) {
        emojiSet = emojiCollections.food;
    } else if (topicLower.includes('travel') || topicLower.includes('vacation') || topicLower.includes('trip')) {
        emojiSet = emojiCollections.travel;
    } else if (topicLower.includes('fitness') || topicLower.includes('workout') || topicLower.includes('gym')) {
        emojiSet = emojiCollections.fitness;
    } else if (topicLower.includes('fashion') || topicLower.includes('style') || topicLower.includes('outfit')) {
        emojiSet = emojiCollections.fashion;
    } else if (topicLower.includes('tech') || topicLower.includes('technology') || topicLower.includes('digital')) {
        emojiSet = emojiCollections.technology;
    }
    
    // Add a few random emojis from the set
    const selectedEmojis = [];
    for (let i = 0; i < 2; i++) {
        const randomEmoji = emojiSet[Math.floor(Math.random() * emojiSet.length)];
        if (!selectedEmojis.includes(randomEmoji)) {
            selectedEmojis.push(randomEmoji);
        }
    }
    
    return caption + ' ' + selectedEmojis.join(' ');
}

function generateHashtags(topic, platform) {
    const topicWords = topic.toLowerCase().split(/\s+/);
    let hashtags = [];
    
    // Add topic-based hashtags
    topicWords.forEach(word => {
        if (word.length > 3) {
            hashtags.push('#' + word.replace(/[^a-zA-Z0-9]/g, ''));
        }
    });
    
    // Add relevant hashtags based on topic
    const topicLower = topic.toLowerCase();
    if (topicLower.includes('business') || topicLower.includes('work')) {
        hashtags = hashtags.concat(hashtagCollections.business.slice(0, 5));
    } else if (topicLower.includes('food') || topicLower.includes('cooking')) {
        hashtags = hashtags.concat(hashtagCollections.food.slice(0, 5));
    } else if (topicLower.includes('travel') || topicLower.includes('vacation')) {
        hashtags = hashtags.concat(hashtagCollections.travel.slice(0, 5));
    } else if (topicLower.includes('fitness') || topicLower.includes('workout')) {
        hashtags = hashtags.concat(hashtagCollections.fitness.slice(0, 5));
    } else if (topicLower.includes('fashion') || topicLower.includes('style')) {
        hashtags = hashtags.concat(hashtagCollections.fashion.slice(0, 5));
    } else if (topicLower.includes('tech') || topicLower.includes('technology')) {
        hashtags = hashtags.concat(hashtagCollections.technology.slice(0, 5));
    } else {
        hashtags = hashtags.concat(hashtagCollections.general.slice(0, 5));
    }
    
    // Platform-specific hashtag limits
    let maxHashtags = 30; // Instagram default
    if (platform === 'twitter') maxHashtags = 2;
    if (platform === 'linkedin') maxHashtags = 5;
    if (platform === 'facebook') maxHashtags = 5;
    
    // Remove duplicates and limit count
    hashtags = [...new Set(hashtags)].slice(0, maxHashtags);
    
    return hashtags.join(' ');
}

function optimizeForPlatform(caption, platform) {
    switch (platform) {
        case 'twitter':
            // Twitter has character limit
            if (caption.length > 280) {
                caption = caption.substring(0, 277) + '...';
            }
            break;
        case 'linkedin':
            // LinkedIn prefers professional tone
            caption = caption.replace(/🔥/g, '⭐').replace(/💯/g, '✅');
            break;
        case 'tiktok':
            // TikTok loves trending hashtags
            caption += ' #fyp #viral #trending';
            break;
    }
    return caption;
}

function displayCaptions(captions) {
    const captionsContainer = document.getElementById('captionsContainer');
    
    let html = '<div class="space-y-4">';
    
    captions.forEach((caption, index) => {
        html += `
            <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="text-lg font-semibold text-gray-800">Caption ${index + 1}</h4>
                    <div class="flex space-x-2">
                        <button onclick="copyCaptionText('${escapeForJs(caption.text)}')" 
                                class="px-3 py-1 bg-pink-600 text-white rounded-lg text-sm font-semibold hover:bg-pink-700 transition-colors">
                            Copy
                        </button>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 mb-3">
                    <p class="text-gray-800 whitespace-pre-wrap">${escapeHtml(caption.text)}</p>
                </div>
                <div class="flex justify-between text-sm text-gray-600">
                    <span>Characters: ${caption.characterCount}</span>
                    <span>Words: ${caption.wordCount}</span>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    captionsContainer.innerHTML = html;
}

function copyCaptionText(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccessMessage('Caption copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showSuccessMessage('Caption copied to clipboard!');
    });
}

function showLoading() {
    const captionsContainer = document.getElementById('captionsContainer');
    captionsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto mb-4"></div>
            <p class="text-lg font-medium text-gray-700">Generating captions...</p>
            <p class="text-sm text-gray-500 mt-2">Creating engaging content for you</p>
        </div>
    `;
}

function showError(message) {
    const captionsContainer = document.getElementById('captionsContainer');
    captionsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-lg font-medium text-red-700">${escapeHtml(message)}</p>
        </div>
    `;
}

function showSuccessMessage(message) {
    // Create and show a temporary success message
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    successDiv.innerHTML = `
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(successDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function escapeForJs(text) {
    return text.replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for textarea
    document.getElementById('contentTopic').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            generateCaptions();
        }
    });
});
