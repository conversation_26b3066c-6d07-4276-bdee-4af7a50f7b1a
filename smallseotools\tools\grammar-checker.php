<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grammar Checker - Free Online Grammar Check Tool</title>
    <meta name="description" content="Check your grammar and spelling with our free online grammar checker. Fix grammar mistakes and improve your writing.">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 10px 0;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
        }
        
        .header-links {
            display: flex;
            gap: 20px;
        }
        
        .header-links a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        
        .header-links a:hover {
            color: #007bff;
        }
        
        /* Main Content */
        .main-content {
            background: #fff;
            margin: 20px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tool-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 250px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            outline: none;
            font-family: inherit;
        }
        
        .form-textarea:focus {
            border-color: #007bff;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .result-title {
            font-size: 20px;
            margin-bottom: 15px;
            color: #333;
        }
        
        .grammar-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .issues-list {
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        
        .issue-item {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .issue-item:last-child {
            border-bottom: none;
        }
        
        .issue-type {
            font-weight: bold;
            color: #dc3545;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        
        .issue-text {
            background: #fff3cd;
            padding: 5px 8px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 5px;
        }
        
        .issue-suggestion {
            color: #666;
            font-size: 14px;
        }
        
        .no-issues {
            text-align: center;
            padding: 40px;
            color: #28a745;
        }
        
        .no-issues h3 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 10px;
            }
            
            .main-content {
                margin: 10px 0;
                padding: 20px;
            }
            
            .tool-title {
                font-size: 24px;
            }
            
            .grammar-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <a href="../index.php" class="logo">Small SEO Tools</a>
                <div class="header-links">
                    <a href="../login.php">Login</a>
                    <a href="../pricing.php">Pricing</a>
                    <a href="../contact.php">Contact</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <h1 class="tool-title">Grammar Checker</h1>
            <p class="tool-description">
                Check your text for grammar and spelling errors with our advanced grammar checker. 
                Get suggestions to improve your writing and ensure error-free content.
            </p>
            
            <form id="grammarForm">
                <div class="form-group">
                    <label for="textInput" class="form-label">Enter your text to check for grammar errors:</label>
                    <textarea 
                        id="textInput" 
                        class="form-textarea" 
                        placeholder="Type or paste your text here to check for grammar and spelling errors..."
                    ></textarea>
                </div>
                
                <button type="submit" class="btn" id="checkBtn">Check Grammar</button>
            </form>
            
            <div id="resultSection" class="result-section">
                <div id="loadingDiv" class="loading">
                    <p>Checking grammar and spelling... Please wait.</p>
                </div>
                
                <div id="resultDiv" style="display: none;">
                    <h3 class="result-title">Grammar Check Results</h3>
                    
                    <div class="grammar-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalErrors">0</div>
                            <div class="stat-label">Total Issues</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="grammarErrors">0</div>
                            <div class="stat-label">Grammar</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="spellingErrors">0</div>
                            <div class="stat-label">Spelling</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="punctuationErrors">0</div>
                            <div class="stat-label">Punctuation</div>
                        </div>
                    </div>
                    
                    <div id="issuesList" class="issues-list"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Small SEO Tools. All rights reserved.</p>
        </div>
    </footer>

    <script>
        const textInput = document.getElementById('textInput');
        const grammarForm = document.getElementById('grammarForm');
        const checkBtn = document.getElementById('checkBtn');
        const resultSection = document.getElementById('resultSection');
        const loadingDiv = document.getElementById('loadingDiv');
        const resultDiv = document.getElementById('resultDiv');
        const totalErrors = document.getElementById('totalErrors');
        const grammarErrors = document.getElementById('grammarErrors');
        const spellingErrors = document.getElementById('spellingErrors');
        const punctuationErrors = document.getElementById('punctuationErrors');
        const issuesList = document.getElementById('issuesList');

        // Form submission
        grammarForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const text = textInput.value.trim();
            if (!text) {
                alert('Please enter some text to check for grammar errors.');
                return;
            }
            
            checkGrammar(text);
        });

        function checkGrammar(text) {
            resultSection.style.display = 'block';
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            checkBtn.disabled = true;
            
            // Simulate grammar checking
            setTimeout(() => {
                showGrammarResults(text);
            }, 2000);
        }

        function showGrammarResults(text) {
            loadingDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            checkBtn.disabled = false;
            
            // Simulate grammar check results
            const issues = generateMockIssues(text);
            
            // Update stats
            const grammarCount = issues.filter(issue => issue.type === 'Grammar').length;
            const spellingCount = issues.filter(issue => issue.type === 'Spelling').length;
            const punctuationCount = issues.filter(issue => issue.type === 'Punctuation').length;
            
            totalErrors.textContent = issues.length;
            grammarErrors.textContent = grammarCount;
            spellingErrors.textContent = spellingCount;
            punctuationErrors.textContent = punctuationCount;
            
            // Display issues
            if (issues.length === 0) {
                issuesList.innerHTML = `
                    <div class="no-issues">
                        <h3>✓ No Issues Found</h3>
                        <p>Your text appears to be grammatically correct!</p>
                    </div>
                `;
            } else {
                issuesList.innerHTML = issues.map(issue => `
                    <div class="issue-item">
                        <div class="issue-type">${issue.type} Error</div>
                        <div class="issue-text">${issue.text}</div>
                        <div class="issue-suggestion">${issue.suggestion}</div>
                    </div>
                `).join('');
            }
        }

        function generateMockIssues(text) {
            const issues = [];
            const words = text.split(/\s+/);
            
            // Common grammar/spelling issues to simulate
            const commonIssues = [
                { pattern: /\bthere\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "their" or "they\'re"' },
                { pattern: /\bits\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "it\'s" (it is)' },
                { pattern: /\byour\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "you\'re" (you are)' },
                { pattern: /\bthen\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "than" for comparisons' },
                { pattern: /\beffect\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "affect" (verb)' },
                { pattern: /\bwho\b/gi, type: 'Grammar', suggestion: 'Consider if you meant "whom" (object)' }
            ];
            
            // Randomly add some issues (simulate detection)
            if (Math.random() > 0.7) { // 30% chance of having issues
                const numIssues = Math.floor(Math.random() * 3) + 1;
                for (let i = 0; i < numIssues; i++) {
                    const randomIssue = commonIssues[Math.floor(Math.random() * commonIssues.length)];
                    const match = text.match(randomIssue.pattern);
                    if (match) {
                        issues.push({
                            type: randomIssue.type,
                            text: match[0],
                            suggestion: randomIssue.suggestion
                        });
                    }
                }
            }
            
            return issues;
        }
    </script>
</body>
</html>
