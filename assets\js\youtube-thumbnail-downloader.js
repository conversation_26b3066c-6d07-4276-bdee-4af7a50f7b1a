// YouTube Thumbnail Downloader Functionality

function extractThumbnails() {
    const videoUrl = document.getElementById('videoUrl').value.trim();
    
    if (!videoUrl) {
        showError('Please enter a YouTube video URL');
        return;
    }
    
    const videoId = extractVideoId(videoUrl);
    
    if (!videoId) {
        showError('Invalid YouTube URL. Please enter a valid YouTube video URL.');
        return;
    }
    
    generateThumbnails(videoId);
}

function extractVideoId(url) {
    // Regular expressions for different YouTube URL formats
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|m\.youtube\.com\/watch\?v=)([^&\n?#]+)/,
        /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            return match[1];
        }
    }
    
    return null;
}

function generateThumbnails(videoId) {
    const thumbnailsContainer = document.getElementById('thumbnailsContainer');
    
    // Show loading state
    thumbnailsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
            <p class="text-lg font-medium text-gray-700">Extracting thumbnails...</p>
        </div>
    `;
    
    // Simulate loading delay for better UX
    setTimeout(() => {
        displayThumbnails(videoId);
    }, 1000);
}

function displayThumbnails(videoId) {
    const thumbnailsContainer = document.getElementById('thumbnailsContainer');
    
    // YouTube thumbnail URLs and their descriptions
    const thumbnails = [
        {
            name: 'Default Thumbnail',
            size: '120x90',
            quality: 'Low',
            url: `https://img.youtube.com/vi/${videoId}/default.jpg`
        },
        {
            name: 'Medium Quality',
            size: '320x180',
            quality: 'Medium',
            url: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
        },
        {
            name: 'High Quality',
            size: '480x360',
            quality: 'High',
            url: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
        },
        {
            name: 'Standard Definition',
            size: '640x480',
            quality: 'SD',
            url: `https://img.youtube.com/vi/${videoId}/sddefault.jpg`
        },
        {
            name: 'Maximum Resolution',
            size: '1280x720',
            quality: 'HD',
            url: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
        }
    ];
    
    let html = `
        <div class="mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-2">Video ID: ${videoId}</h3>
            <p class="text-sm text-gray-600">Click on any thumbnail to download it in full resolution</p>
        </div>
        <div class="space-y-4">
    `;
    
    thumbnails.forEach((thumbnail, index) => {
        html += `
            <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-shadow">
                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <div class="flex-shrink-0">
                        <img src="${thumbnail.url}" 
                             alt="${thumbnail.name}" 
                             class="rounded-lg border border-gray-300 max-w-full h-auto"
                             style="max-width: 160px; max-height: 120px;"
                             onerror="handleImageError(this, '${thumbnail.name}')">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-lg font-semibold text-gray-800 mb-1">${thumbnail.name}</h4>
                        <div class="space-y-1 text-sm text-gray-600">
                            <p><span class="font-medium">Resolution:</span> ${thumbnail.size}</p>
                            <p><span class="font-medium">Quality:</span> ${thumbnail.quality}</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2 w-full sm:w-auto">
                        <button onclick="downloadThumbnail('${thumbnail.url}', '${videoId}_${thumbnail.quality.toLowerCase()}.jpg')" 
                                class="px-4 py-2 bg-red-600 text-white rounded-lg font-semibold hover:bg-red-700 transition-colors flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span>Download</span>
                        </button>
                        <button onclick="copyImageUrl('${thumbnail.url}')" 
                                class="px-4 py-2 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            <span>Copy URL</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
        </div>
        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 class="font-bold text-blue-800 mb-2">💡 Pro Tips:</h4>
            <ul class="text-sm text-blue-700 space-y-1">
                <li>• Use Maximum Resolution (HD) for best quality thumbnails</li>
                <li>• Some videos may not have all resolution options available</li>
                <li>• Thumbnails are directly from YouTube's servers</li>
                <li>• Right-click and "Save Image As" for alternative download method</li>
            </ul>
        </div>
    `;
    
    thumbnailsContainer.innerHTML = html;
}

function downloadThumbnail(imageUrl, filename) {
    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    link.target = '_blank';
    
    // For cross-origin images, we need to fetch and create blob
    fetch(imageUrl)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        })
        .catch(error => {
            // Fallback: open in new tab
            window.open(imageUrl, '_blank');
        });
}

function copyImageUrl(imageUrl) {
    navigator.clipboard.writeText(imageUrl).then(() => {
        // Show success message
        showSuccessMessage('Image URL copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = imageUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showSuccessMessage('Image URL copied to clipboard!');
    });
}

function handleImageError(img, thumbnailName) {
    img.style.display = 'none';
    const errorDiv = document.createElement('div');
    errorDiv.className = 'flex items-center justify-center w-40 h-30 bg-gray-100 border border-gray-300 rounded-lg';
    errorDiv.innerHTML = `
        <div class="text-center text-gray-500">
            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-xs">Not Available</p>
        </div>
    `;
    img.parentNode.insertBefore(errorDiv, img);
}

function showError(message) {
    const thumbnailsContainer = document.getElementById('thumbnailsContainer');
    thumbnailsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-lg font-medium text-red-700">${escapeHtml(message)}</p>
            <p class="text-sm text-red-600 mt-2">Please check the URL and try again</p>
        </div>
    `;
}

function showSuccessMessage(message) {
    // Create and show a temporary success message
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    successDiv.innerHTML = `
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(successDiv);
    
    // Animate in
    setTimeout(() => {
        successDiv.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 300);
    }, 3000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for URL input
    document.getElementById('videoUrl').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            extractThumbnails();
        }
    });
    
    // Auto-extract when URL is pasted
    document.getElementById('videoUrl').addEventListener('paste', function(e) {
        setTimeout(() => {
            const url = e.target.value.trim();
            if (url && extractVideoId(url)) {
                extractThumbnails();
            }
        }, 100);
    });
});
