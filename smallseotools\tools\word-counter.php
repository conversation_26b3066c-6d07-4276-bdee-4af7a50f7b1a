<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word Counter - Free Online Word Count Tool</title>
    <meta name="description" content="Count words, characters, paragraphs, and sentences in your text with our free online word counter tool.">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 10px 0;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
        }
        
        .header-links {
            display: flex;
            gap: 20px;
        }
        
        .header-links a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        
        .header-links a:hover {
            color: #007bff;
        }
        
        /* Main Content */
        .main-content {
            background: #fff;
            margin: 20px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tool-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 300px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            outline: none;
            font-family: inherit;
        }
        
        .form-textarea:focus {
            border-color: #007bff;
        }
        
        .toolbar {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .reading-time {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
        
        .reading-time h4 {
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 10px;
            }
            
            .main-content {
                margin: 10px 0;
                padding: 20px;
            }
            
            .tool-title {
                font-size: 24px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .toolbar {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <a href="../index.php" class="logo">Small SEO Tools</a>
                <div class="header-links">
                    <a href="../login.php">Login</a>
                    <a href="../pricing.php">Pricing</a>
                    <a href="../contact.php">Contact</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <h1 class="tool-title">Word Counter</h1>
            <p class="tool-description">
                Count words, characters, paragraphs, and sentences in your text. 
                Get detailed statistics about your content including reading time and keyword density.
            </p>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="wordCount">0</div>
                    <div class="stat-label">Words</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="charCount">0</div>
                    <div class="stat-label">Characters</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="charCountNoSpaces">0</div>
                    <div class="stat-label">Characters (no spaces)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="sentenceCount">0</div>
                    <div class="stat-label">Sentences</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="paragraphCount">0</div>
                    <div class="stat-label">Paragraphs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgWordsPerSentence">0</div>
                    <div class="stat-label">Avg Words/Sentence</div>
                </div>
            </div>
            
            <!-- Text Input -->
            <div class="form-group">
                <label for="textInput" class="form-label">Enter your text:</label>
                <textarea 
                    id="textInput" 
                    class="form-textarea" 
                    placeholder="Start typing or paste your text here..."
                ></textarea>
                
                <div class="toolbar">
                    <button class="btn" onclick="clearText()">Clear Text</button>
                    <button class="btn btn-secondary" onclick="copyText()">Copy Text</button>
                    <button class="btn btn-secondary" onclick="selectAllText()">Select All</button>
                </div>
            </div>
            
            <!-- Reading Time -->
            <div class="reading-time">
                <h4>Reading Time</h4>
                <p id="readingTime">0 minutes</p>
                <small>Based on average reading speed of 200 words per minute</small>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Small SEO Tools. All rights reserved.</p>
        </div>
    </footer>

    <script>
        const textInput = document.getElementById('textInput');
        const wordCount = document.getElementById('wordCount');
        const charCount = document.getElementById('charCount');
        const charCountNoSpaces = document.getElementById('charCountNoSpaces');
        const sentenceCount = document.getElementById('sentenceCount');
        const paragraphCount = document.getElementById('paragraphCount');
        const avgWordsPerSentence = document.getElementById('avgWordsPerSentence');
        const readingTime = document.getElementById('readingTime');

        function updateStats() {
            const text = textInput.value;
            
            // Word count
            const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
            wordCount.textContent = words;
            
            // Character count
            charCount.textContent = text.length;
            
            // Character count without spaces
            charCountNoSpaces.textContent = text.replace(/\s/g, '').length;
            
            // Sentence count
            const sentences = text.trim() === '' ? 0 : text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
            sentenceCount.textContent = sentences;
            
            // Paragraph count
            const paragraphs = text.trim() === '' ? 0 : text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
            paragraphCount.textContent = paragraphs;
            
            // Average words per sentence
            const avgWords = sentences > 0 ? Math.round(words / sentences) : 0;
            avgWordsPerSentence.textContent = avgWords;
            
            // Reading time (200 words per minute)
            const minutes = Math.ceil(words / 200);
            if (minutes === 0) {
                readingTime.textContent = '0 minutes';
            } else if (minutes === 1) {
                readingTime.textContent = '1 minute';
            } else {
                readingTime.textContent = minutes + ' minutes';
            }
        }

        function clearText() {
            textInput.value = '';
            updateStats();
            textInput.focus();
        }

        function copyText() {
            textInput.select();
            document.execCommand('copy');
            alert('Text copied to clipboard!');
        }

        function selectAllText() {
            textInput.select();
        }

        // Update stats on input
        textInput.addEventListener('input', updateStats);
        textInput.addEventListener('paste', () => {
            setTimeout(updateStats, 10);
        });

        // Initialize
        updateStats();
    </script>
</body>
</html>
