/* Small SEO Tools - Main Stylesheet */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 10px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.logo:hover {
    color: #0056b3;
}

.header-links {
    display: flex;
    gap: 20px;
}

.header-links a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.header-links a:hover {
    color: #007bff;
}

/* Navigation Styles */
.nav {
    background: #f8f9fa;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
}

.nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
    flex-wrap: wrap;
}

.nav-links a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #007bff;
}

/* Button Styles */
.btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
}

.btn-danger:hover {
    background: #c82333;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: #007bff;
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* Card Styles */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-title {
    font-size: 20px;
    margin-bottom: 10px;
    color: #333;
}

.card-text {
    color: #666;
    line-height: 1.6;
}

/* Grid Styles */
.grid {
    display: grid;
    gap: 20px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.p-10 {
    padding: 10px;
}

.p-20 {
    padding: 20px;
}

.p-30 {
    padding: 30px;
}

/* Color Classes */
.text-primary {
    color: #007bff;
}

.text-success {
    color: #28a745;
}

.text-danger {
    color: #dc3545;
}

.text-warning {
    color: #ffc107;
}

.text-info {
    color: #17a2b8;
}

.text-muted {
    color: #6c757d;
}

/* Background Classes */
.bg-primary {
    background-color: #007bff;
    color: white;
}

.bg-success {
    background-color: #28a745;
    color: white;
}

.bg-danger {
    background-color: #dc3545;
    color: white;
}

.bg-warning {
    background-color: #ffc107;
    color: #333;
}

.bg-info {
    background-color: #17a2b8;
    color: white;
}

.bg-light {
    background-color: #f8f9fa;
}

.bg-dark {
    background-color: #343a40;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-top {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav-links {
        gap: 15px;
        justify-content: center;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .card {
        padding: 15px;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 10px;
    }
}
