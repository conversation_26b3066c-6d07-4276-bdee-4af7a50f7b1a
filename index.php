<?php
require_once 'config/config.php';

$page_title = 'ToolsForge - 100+ Free SEO & Digital Marketing Tools';
$page_description = 'Free online SEO tools, content optimization, image compression, plagiarism checker, and 100+ digital marketing tools. Forge your digital success with professional-grade tools.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo get_page_title($page_title); ?></title>
    <meta name="description" content="<?php echo get_page_description($page_description); ?>">
    <meta name="keywords" content="<?php echo DEFAULT_KEYWORDS; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="apple-touch-icon.png">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo get_page_title($page_title); ?>">
    <meta property="og:description" content="<?php echo get_page_description($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo get_page_title($page_title); ?>">
    <meta name="twitter:description" content="<?php echo get_page_description($page_description); ?>">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/fonts/fonts.css">
    <link rel="stylesheet" href="assets/css/tailwind.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Custom Hero Animations -->
    <style>
        @keyframes spin-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(12deg); }
            50% { transform: translateY(-20px) rotate(12deg); }
        }

        @keyframes bounce-slow {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .animate-spin-slow {
            animation: spin-slow 20s linear infinite;
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .animate-bounce-slow {
            animation: bounce-slow 4s ease-in-out infinite;
        }

        /* Hero text gradient animation */
        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero-gradient-text {
            background: linear-gradient(-45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd);
            background-size: 400% 400%;
            animation: gradient-shift 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
    
    <!-- JSON-LD Schema with International Targeting -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "LoganixSEO",
        "url": "https://loganixseo.com",
        "logo": "https://loganixseo.com/images/logo.png",
        "description": "Professional SEO services, guest posting, and link building. Get high-authority backlinks from real websites with traffic.",
        "areaServed": [
            {
                "@type": "Country",
                "name": "Ireland"
            },
            {
                "@type": "Country",
                "name": "Netherlands"
            },
            {
                "@type": "Country",
                "name": "Belgium"
            },
            {
                "@type": "Country",
                "name": "United States"
            }
        ],
        "sameAs": [
            "https://twitter.com/loganixseo",
            "https://linkedin.com/company/loganixseo"
        ],
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "telephone": "******-123-4567",
                "contactType": "customer service",
                "areaServed": "US"
            },
            {
                "@type": "ContactPoint",
                "telephone": "+353-1-555-0123",
                "contactType": "customer service",
                "areaServed": "IE"
            },
            {
                "@type": "ContactPoint",
                "telephone": "+31-20-555-0123",
                "contactType": "customer service",
                "areaServed": "NL"
            },
            {
                "@type": "ContactPoint",
                "telephone": "+32-2-555-0123",
                "contactType": "customer service",
                "areaServed": "BE"
            }
        ],
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "SEO Services",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Guest Posting Services",
                        "description": "High-authority guest posts with do-follow backlinks"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Link Building Services",
                        "description": "Comprehensive link building strategies for improved rankings"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Content Writing Services",
                        "description": "SEO-optimized content creation and marketing"
                    }
                }
            ]
        }
    }
    </script>
</head>
<body class="font-poppins">
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="relative py-16 lg:py-24 overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <!-- Modern Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #ffffff 2px, transparent 2px), radial-gradient(circle at 75% 75%, #ffffff 2px, transparent 2px); background-size: 50px 50px;"></div>
            </div>

            <!-- Geometric Shapes -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute top-20 left-20 w-32 h-32 border-2 border-white/20 rounded-full animate-spin-slow"></div>
                <div class="absolute bottom-20 right-20 w-24 h-24 border-2 border-white/30 rotate-45 animate-pulse"></div>
                <div class="absolute top-1/2 left-10 w-16 h-16 bg-white/10 rounded-lg rotate-12 animate-float"></div>
                <div class="absolute bottom-1/3 left-1/3 w-20 h-20 bg-white/5 rounded-full animate-bounce-slow"></div>
            </div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-7xl">
                <div class="text-center animate-on-scroll">
                    <!-- Main Heading -->
                    <div class="mb-12">
                        <h1 class="hero-title text-4xl lg:text-6xl font-black text-white mb-6 leading-tight text-shadow-lg">
                            <span class="relative inline-block">
                                <span class="gradient-text font-black text-shadow-glow" style="font-size: 1.1em;">
                                    ToolsForge
                                </span>
                                <div class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 via-pink-500 to-cyan-400 rounded-full animate-pulse"></div>
                            </span>
                        </h1>
                        <p class="hero-subtitle text-xl lg:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed font-light mb-8">
                            <span class="font-semibold text-yellow-300">100+ Free SEO & Digital Marketing Tools</span> to optimize your website,
                            analyze performance, and dominate search rankings. Forge your digital success with professional-grade tools.
                        </p>

                        <!-- Search Bar -->
                        <div class="max-w-2xl mx-auto mb-8">
                            <div class="relative">
                                <input type="text" id="toolSearch" placeholder="Search from 100+ tools... (e.g., plagiarism checker, image compressor)"
                                       class="w-full px-6 py-4 pr-16 text-lg rounded-2xl border-0 shadow-2xl focus:outline-none focus:ring-4 focus:ring-yellow-400/50 bg-white/95 backdrop-blur-sm text-gray-800 placeholder-gray-500">
                                <button class="absolute right-2 top-2 bottom-2 px-6 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-xl font-bold hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 transform hover:scale-105">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </button>
                            </div>
                            <!-- Search Results Dropdown -->
                            <div id="searchResults" class="absolute left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-200 max-h-96 overflow-y-auto z-50 hidden">
                                <!-- Search results will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                        <a href="pages/tools.php" class="group relative px-8 py-4 bg-gradient-to-r from-pink-500 to-violet-600 rounded-2xl text-white font-bold text-lg shadow-2xl hover:shadow-pink-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden">
                            <span class="relative z-10 flex items-center space-x-3">
                                <span>Browse All Tools</span>
                                <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-violet-600 to-pink-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </a>

                        <a href="tools/plagiarism-checker.php" class="group px-8 py-4 bg-white/10 backdrop-blur-sm border-2 border-white/20 rounded-2xl text-white font-bold text-lg hover:bg-white/20 transform hover:scale-105 transition-all duration-300">
                            <span class="flex items-center space-x-3">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Plagiarism Checker</span>
                            </span>
                        </a>

                        <a href="tools/image-compressor.php" class="group px-8 py-4 bg-white/10 backdrop-blur-sm border-2 border-white/20 rounded-2xl text-white font-bold text-lg hover:bg-white/20 transform hover:scale-105 transition-all duration-300">
                            <span class="flex items-center space-x-3">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span>Image Compressor</span>
                            </span>
                        </a>
                    </div>

                    <!-- Achievement Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
                        <div class="group text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300">
                            <div class="text-4xl font-black text-yellow-400 mb-2 group-hover:scale-110 transition-transform">100+</div>
                            <div class="text-white/80 font-medium text-sm">Free Tools</div>
                        </div>
                        <div class="group text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300">
                            <div class="text-4xl font-black text-pink-400 mb-2 group-hover:scale-110 transition-transform">1M+</div>
                            <div class="text-white/80 font-medium text-sm">Users Monthly</div>
                        </div>
                        <div class="group text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300">
                            <div class="text-4xl font-black text-cyan-400 mb-2 group-hover:scale-110 transition-transform">100%</div>
                            <div class="text-white/80 font-medium text-sm">Free</div>
                        </div>
                        <div class="group text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300">
                            <div class="text-4xl font-black text-green-400 mb-2 group-hover:scale-110 transition-transform">24/7</div>
                            <div class="text-white/80 font-medium text-sm">Available</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Tools Section -->
        <section class="py-16 lg:py-20 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                <div class="text-center mb-12 animate-on-scroll">
                    <h2 class="text-3xl lg:text-4xl font-bold mb-4" style="background: linear-gradient(45deg, #9333EA, #EC4899, #EF4444, #F97316, #EAB308, #22C55E, #22D3EE, #3B82F6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">
                        Most Popular Tools
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Discover our most-used SEO and digital marketing tools trusted by millions of users worldwide
                    </p>
                </div>

                <!-- Popular Tools Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                    <!-- Tool 1: Plagiarism Checker -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-red-100 hover:border-red-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #EF4444, #DC2626);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Plagiarism Checker</h3>
                            <p class="text-gray-600 text-sm mb-4">Check for duplicate content and ensure originality</p>
                            <a href="tools/plagiarism-checker.php" class="inline-flex items-center text-red-600 font-semibold hover:text-red-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 2: Grammar Checker -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-blue-100 hover:border-blue-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Grammar Checker</h3>
                            <p class="text-gray-600 text-sm mb-4">Fix grammar and spelling errors instantly</p>
                            <a href="tools/grammar-checker.php" class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 3: Paraphrasing Tool -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-green-100 hover:border-green-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #10B981, #059669);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Paraphrasing Tool</h3>
                            <p class="text-gray-600 text-sm mb-4">Rewrite content while maintaining meaning</p>
                            <a href="tools/paraphrasing-tool.php" class="inline-flex items-center text-green-600 font-semibold hover:text-green-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 4: Image Compressor -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-purple-100 hover:border-purple-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Image Compressor</h3>
                            <p class="text-gray-600 text-sm mb-4">Compress images without losing quality</p>
                            <a href="tools/image-compressor.php" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 5: Backlink Checker -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-cyan-100 hover:border-cyan-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #06B6D4, #0891B2);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Backlink Checker</h3>
                            <p class="text-gray-600 text-sm mb-4">Analyze your website's backlink profile</p>
                            <a href="tools/backlink-checker.php" class="inline-flex items-center text-cyan-600 font-semibold hover:text-cyan-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 6: Domain Authority Checker -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-orange-100 hover:border-orange-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #F97316, #EA580C);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Domain Authority</h3>
                            <p class="text-gray-600 text-sm mb-4">Check domain authority and SEO metrics</p>
                            <a href="tools/domain-authority-checker.php" class="inline-flex items-center text-orange-600 font-semibold hover:text-orange-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 7: Word Counter -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-pink-100 hover:border-pink-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #EC4899, #DB2777);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Word Counter</h3>
                            <p class="text-gray-600 text-sm mb-4">Count words, characters, and paragraphs</p>
                            <a href="tools/word-counter.php" class="inline-flex items-center text-pink-600 font-semibold hover:text-pink-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 8: Meta Tag Generator -->
                    <div class="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-yellow-100 hover:border-yellow-300">
                        <div class="p-6">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #EAB308, #CA8A04);">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold mb-2 text-gray-800">Meta Tag Generator</h3>
                            <p class="text-gray-600 text-sm mb-4">Generate SEO-optimized meta tags</p>
                            <a href="tools/meta-tag-generator.php" class="inline-flex items-center text-yellow-600 font-semibold hover:text-yellow-700 transition-colors">
                                Use Tool <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- View All Tools Button -->
                <div class="text-center">
                    <a href="pages/tools.php" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                        <span>View All 100+ Tools</span>
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>

        <!-- Tool Categories Section -->
        <section class="py-16 lg:py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                <div class="text-center mb-12 animate-on-scroll">
                    <h2 class="text-3xl lg:text-4xl font-bold mb-4" style="background: linear-gradient(45deg, #9333EA, #EC4899, #EF4444, #F97316, #EAB308, #22C55E, #22D3EE, #3B82F6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">
                        Tool Categories
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Explore our comprehensive collection of tools organized by category
                    </p>
                </div>

                <!-- Categories Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- SEO Tools Category -->
                    <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-blue-100 hover:border-blue-300 p-8">
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4 text-gray-800">SEO Tools</h3>
                            <p class="text-gray-600 mb-6">Comprehensive SEO analysis and optimization tools</p>
                            <div class="text-sm text-gray-500 mb-6">
                                Backlink Checker • Domain Authority • Keyword Research • SERP Checker • More...
                            </div>
                            <a href="pages/tools.php#seo" class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors">
                                Explore SEO Tools <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Writing Tools Category -->
                    <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-green-100 hover:border-green-300 p-8">
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg" style="background: linear-gradient(135deg, #10B981, #059669);">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4 text-gray-800">Writing Tools</h3>
                            <p class="text-gray-600 mb-6">Perfect your content with advanced writing tools</p>
                            <div class="text-sm text-gray-500 mb-6">
                                Plagiarism Checker • Grammar Checker • Paraphrasing • Word Counter • More...
                            </div>
                            <a href="pages/tools.php#writing" class="inline-flex items-center text-green-600 font-semibold hover:text-green-700 transition-colors">
                                Explore Writing Tools <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>

                    <!-- Image Tools Category -->
                    <div class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 border-purple-100 hover:border-purple-300 p-8">
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED);">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4 text-gray-800">Image Tools</h3>
                            <p class="text-gray-600 mb-6">Optimize and enhance your images effortlessly</p>
                            <div class="text-sm text-gray-500 mb-6">
                                Image Compressor • Image Resizer • Favicon Generator • Logo Maker • More...
                            </div>
                            <a href="pages/tools.php#image" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors">
                                Explore Image Tools <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);"
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 left-0 w-full h-full" style="background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(251, 146, 60, 0.2), rgba(250, 204, 21, 0.2), rgba(74, 222, 128, 0.2), rgba(34, 211, 238, 0.2));"></div>
            </div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center mb-10 animate-on-scroll">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-3" style="background: linear-gradient(45deg, #9333EA, #EC4899, #EF4444, #F97316, #EAB308, #22C55E, #22D3EE, #3B82F6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Our Expert SEO Services</h2>
                    <p class="text-lg text-gray-700 max-w-2xl mx-auto font-medium">
                        Comprehensive SEO solutions designed to improve your search rankings and drive organic traffic
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Service 1 -->
                    <div class="card-hover rounded-xl shadow-lg p-6 border-2 animate-on-scroll transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(135deg, #FDF2F8, #FCE7F3, #FEF2F2); border-color: #F472B6;">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg" style="background: linear-gradient(135deg, #F472B6, #EF4444, #F97316);">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-3" style="background: linear-gradient(45deg, #EC4899, #EF4444, #F97316); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Guest Post Link Building</h3>
                        <p class="text-gray-700 mb-4 font-medium text-sm">High-quality backlinks from authoritative websites in your niche to boost domain authority and search rankings.</p>
                        <a href="pages/link-building.php" class="text-white font-bold px-3 py-2 rounded-full inline-flex items-center space-x-2 transform hover:scale-105 transition-all duration-300 shadow-lg text-sm" style="background: linear-gradient(45deg, #EC4899, #EF4444, #F97316);">
                            <span>Learn More</span>
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Service 2 -->
                    <div class="card-hover rounded-2xl shadow-2xl p-8 border-2 animate-on-scroll transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(135deg, #ECFDF5, #F0FDF4, #F7FEE7); border-color: #4ADE80;">
                        <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-6 shadow-lg" style="background: linear-gradient(135deg, #10B981, #22C55E, #84CC16);">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h2m-4 0V6a2 2 0 012-2h6a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4" style="background: linear-gradient(45deg, #10B981, #22C55E, #84CC16); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Blogger Outreach</h3>
                        <p class="text-gray-700 mb-6 font-medium">Manual blogger outreach to build genuine relationships and earn high-quality editorial backlinks from real bloggers in your niche.</p>
                        <a href="pages/blogger-outreach.php" class="text-white font-bold px-4 py-2 rounded-full inline-flex items-center space-x-2 transform hover:scale-105 transition-all duration-300 shadow-lg" style="background: linear-gradient(45deg, #10B981, #22C55E, #84CC16);">
                            <span>Learn More</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Service 3 -->
                    <div class="card-hover rounded-2xl shadow-2xl p-8 border-2 animate-on-scroll transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(135deg, #ECFEFF, #EFF6FF, #EEF2FF); border-color: #22D3EE;">
                        <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-6 shadow-lg" style="background: linear-gradient(135deg, #22D3EE, #3B82F6, #6366F1);">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4" style="background: linear-gradient(45deg, #22D3EE, #3B82F6, #6366F1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Content Marketing</h3>
                        <p class="text-gray-700 mb-6 font-medium">Strategic content creation and marketing to engage your audience and establish thought leadership in your industry.</p>
                        <a href="pages/content-writing.php" class="text-white font-bold px-4 py-2 rounded-full inline-flex items-center space-x-2 transform hover:scale-105 transition-all duration-300 shadow-lg" style="background: linear-gradient(45deg, #22D3EE, #3B82F6, #6366F1);">
                            <span>Learn More</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-8 lg:py-12"></div>

        <!-- Why Choose Us Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #F3E8FF 0%, #FDF4FF 10%, #FCE7F3 20%, #FFF1F2 30%, #FFF7ED 40%, #FFFBEB 50%, #FEFCE8 60%, #F7FEE7 70%, #ECFDF5 80%, #F0FDFA 90%, #ECFEFF 100%);"
            <!-- Animated Background Shapes -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-10 left-10 w-32 h-32 rounded-full opacity-20 animate-pulse" style="background: linear-gradient(135deg, #A78BFA, #EC4899);"></div>
                <div class="absolute top-40 right-20 w-24 h-24 rounded-full opacity-25 animate-bounce" style="background: linear-gradient(135deg, #22D3EE, #3B82F6);"></div>
                <div class="absolute bottom-20 left-1/4 w-40 h-40 rounded-full opacity-15 animate-pulse" style="background: linear-gradient(135deg, #4ADE80, #10B981);"></div>
                <div class="absolute bottom-40 right-1/3 w-28 h-28 rounded-full opacity-30 animate-bounce" style="background: linear-gradient(135deg, #FACC15, #F97316);"></div>
            </div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <!-- Section Header -->
                <div class="text-center mb-16 animate-on-scroll">
                    <h2 class="hero-title text-4xl lg:text-5xl font-black mb-6 gradient-text text-shadow-lg">
                        Why Choose LoganixSEO?
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        We don't just promise results – we deliver them. Join 500+ businesses that trust us with their SEO success.
                    </p>
                </div>

                <!-- Features Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Feature 1 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg pulse-icon" style="background: linear-gradient(135deg, #22C55E, #16A34A);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">100% Safe & White-Hat</h3>
                                <p class="text-gray-600 leading-relaxed">Only Google-approved techniques. No black-hat risks that could harm your website's future.</p>
                                <div class="mt-4 flex items-center text-sm text-green-600 font-semibold">
                                    <span>Google Guidelines Compliant</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 2 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">Real Websites, Real Traffic</h3>
                                <p class="text-gray-600 leading-relaxed">High-authority backlinks from genuine websites with actual organic traffic and engaged audiences.</p>
                                <div class="mt-4 flex items-center text-sm text-blue-600 font-semibold">
                                    <span>Verified Traffic Sources</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 3 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">Fast Results</h3>
                                <p class="text-gray-600 leading-relaxed">See improvements in 2-4 weeks. Our proven strategies deliver measurable results quickly.</p>
                                <div class="mt-4 flex items-center text-sm text-purple-600 font-semibold">
                                    <span>Quick Turnaround</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 4 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #EC4899, #BE185D);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">500+ Happy Clients</h3>
                                <p class="text-gray-600 leading-relaxed">Trusted by businesses worldwide. Join our growing community of successful clients.</p>
                                <div class="mt-4 flex items-center text-sm text-pink-600 font-semibold">
                                    <span>Proven Track Record</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 5 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #F59E0B, #D97706);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">Transparent Pricing</h3>
                                <p class="text-gray-600 leading-relaxed">No hidden fees. Clear pricing structure with detailed reporting on every investment.</p>
                                <div class="mt-4 flex items-center text-sm text-yellow-600 font-semibold">
                                    <span>No Hidden Costs</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 6 -->
                    <div class="group animate-on-scroll">
                        <div class="feature-card relative p-8 pt-12 rounded-2xl shadow-xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                            <div class="absolute -top-4 left-8">
                                <div class="feature-icon w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #10B981, #059669);">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="pt-4">
                                <h3 class="text-xl font-bold mb-3 text-gray-800">24/7 Support</h3>
                                <p class="text-gray-600 leading-relaxed">Round-the-clock support and regular updates. We're always here when you need us.</p>
                                <div class="mt-4 flex items-center text-sm text-emerald-600 font-semibold">
                                    <span>Always Available</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Stats -->
                <div class="text-center animate-on-scroll">
                    <div class="inline-flex items-center justify-center p-8 rounded-3xl shadow-2xl" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC);">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                            <div class="text-center">
                                <div class="stats-number text-3xl font-black mb-2 gradient-text">500+</div>
                                <div class="text-sm font-semibold text-gray-600">Happy Clients</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-number text-3xl font-black mb-2 gradient-text">4.9/5</div>
                                <div class="text-sm font-semibold text-gray-600">Client Rating</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-number text-3xl font-black mb-2 gradient-text">100%</div>
                                <div class="text-sm font-semibold text-gray-600">Safe Methods</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-number text-3xl font-black mb-2 gradient-text">24/7</div>
                                <div class="text-sm font-semibold text-gray-600">Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-8 lg:py-12"></div>

        <!-- CTA Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #581C87 0%, #86198F 8%, #BE185D 16%, #E11D48 24%, #EF4444 32%, #F97316 40%, #F59E0B 48%, #FDE047 56%, #BEF264 64%, #4ADE80 72%, #10B981 80%, #0D9488 88%, #0E7490 96%, #075985 100%);"
            <!-- Floating Elements -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-1/4 left-1/4 w-6 h-6 rounded-full animate-bounce opacity-70" style="background-color: #FDE047;"></div>
                <div class="absolute top-1/3 right-1/4 w-4 h-4 rounded-full animate-bounce opacity-60" style="background-color: #F472B6; animation-delay: 0.5s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-8 h-8 rounded-full animate-bounce opacity-80" style="background-color: #67E8F9; animation-delay: 1s;"></div>
                <div class="absolute bottom-1/3 right-1/3 w-3 h-3 rounded-full animate-bounce opacity-50" style="background-color: #86EFAC; animation-delay: 1.5s;"></div>
                <div class="absolute top-1/2 left-1/6 w-5 h-5 rounded-full animate-bounce opacity-65" style="background-color: #C4B5FD; animation-delay: 2s;"></div>
                <div class="absolute top-3/4 right-1/6 w-7 h-7 rounded-full animate-bounce opacity-75" style="background-color: #FDBA74; animation-delay: 2.5s;"></div>
            </div>

            <!-- Background Gradient Orbs -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute -top-40 -left-40 w-80 h-80 rounded-full opacity-30 animate-pulse" style="background: linear-gradient(135deg, #EC4899, #7C3AED); filter: blur(40px);"></div>
                <div class="absolute -bottom-40 -right-40 w-80 h-80 rounded-full opacity-30 animate-pulse" style="background: linear-gradient(135deg, #06B6D4, #1D4ED8); filter: blur(40px);"></div>
                <div class="absolute top-1/2 left-1/2 w-96 h-96 rounded-full opacity-20 animate-pulse" style="background: linear-gradient(135deg, #FACC15, #F97316); filter: blur(60px); transform: translate(-50%, -50%);"></div>
            </div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <!-- Main CTA Content -->
                <div class="text-center mb-16 animate-on-scroll">
                    <div class="inline-flex items-center justify-center p-2 rounded-full mb-6" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
                        <span class="px-4 py-2 text-sm font-semibold text-white rounded-full" style="background: linear-gradient(45deg, #22C55E, #16A34A);">
                            🚀 Ready to Dominate?
                        </span>
                    </div>

                    <h2 class="hero-title text-4xl lg:text-6xl font-black text-white mb-6 gradient-text-white text-shadow-xl">
                        Transform Your Business with
                        <span class="block mt-2 gradient-text">Powerful SEO Results</span>
                    </h2>

                    <p class="text-xl lg:text-2xl text-white mb-12 max-w-4xl mx-auto leading-relaxed" style="text-shadow: 1px 1px 3px rgba(0,0,0,0.6);">
                        Join <strong><span class="counter" data-target="500">0</span>+ successful businesses</strong> that have skyrocketed their online presence and dominated their competition with our proven SEO strategies.
                    </p>
                </div>

                <!-- Features Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <!-- Feature 1 -->
                    <div class="text-center animate-on-scroll">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 shadow-2xl" style="background: linear-gradient(135deg, #22C55E, #16A34A);">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">Fast Results</h3>
                        <p class="text-white opacity-90">See improvements in 2-4 weeks</p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="text-center animate-on-scroll">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 shadow-2xl" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">100% Safe</h3>
                        <p class="text-white opacity-90">Google-approved techniques only</p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="text-center animate-on-scroll">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 shadow-2xl" style="background: linear-gradient(135deg, #EC4899, #BE185D);">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">500+ Clients</h3>
                        <p class="text-white opacity-90">Trusted by businesses worldwide</p>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="text-center animate-on-scroll">
                    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                        <a href="pages/contact.php" class="group relative overflow-hidden text-white px-10 py-5 rounded-2xl text-lg font-bold transform hover:scale-105 transition-all duration-300 inline-flex items-center justify-center space-x-3 shadow-2xl" style="background: linear-gradient(45deg, #FACC15, #F97316, #EF4444);">
                            <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                            <svg class="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <span>Start Dominating Now</span>
                            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                            </svg>
                        </a>

                        <a href="pages/write-for-us.php" class="group relative overflow-hidden text-white px-10 py-5 rounded-2xl text-lg font-bold transform hover:scale-105 transition-all duration-300 inline-flex items-center justify-center space-x-3 shadow-2xl border-2 border-white border-opacity-30" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
                            <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                            <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                            <span>Write for Us</span>
                        </a>
                    </div>

                    <!-- Trust Indicators -->
                    <div class="mt-12 flex flex-wrap justify-center items-center gap-8 opacity-80">
                        <div class="flex items-center space-x-2 text-white">
                            <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-semibold">No Setup Fees</span>
                        </div>
                        <div class="flex items-center space-x-2 text-white">
                            <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-semibold">Money-Back Guarantee</span>
                        </div>
                        <div class="flex items-center space-x-2 text-white">
                            <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-semibold">24/7 Support</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/search.js"></script>
</body>
</html>
