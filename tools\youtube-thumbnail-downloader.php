<?php
require_once '../config/config.php';

$page_title = 'YouTube Thumbnail Downloader - Free HD Thumbnail Download';
$page_description = 'Download YouTube video thumbnails in HD quality. Get high-resolution thumbnails from any YouTube video URL. Free and fast thumbnail downloader.';
$page_keywords = 'youtube thumbnail downloader, download youtube thumbnail, youtube thumbnail extractor, video thumbnail download, HD thumbnail';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "YouTube Thumbnail Downloader",
        "description": "Download YouTube video thumbnails in HD quality. Get high-resolution thumbnails from any YouTube video URL.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "Social Media Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "ToolsForge",
            "url": "<?php echo SITE_URL; ?>"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        YouTube Thumbnail Downloader
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Download high-quality thumbnails from any YouTube video. Get HD, Full HD, and 4K resolution thumbnails instantly.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #EF4444;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #EF4444, #DC2626); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">HD Thumbnail Extractor</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Extract and download YouTube video thumbnails in multiple resolutions</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">YouTube Video URL:</label>
                                <input type="url" id="videoUrl" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #FECACA; background: linear-gradient(45deg, #FEF2F2, #FECACA);" placeholder="https://www.youtube.com/watch?v=VIDEO_ID">
                            </div>
                            
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 class="font-semibold text-blue-800 mb-2">Supported URL Formats:</h3>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• https://www.youtube.com/watch?v=VIDEO_ID</li>
                                    <li>• https://youtu.be/VIDEO_ID</li>
                                    <li>• https://m.youtube.com/watch?v=VIDEO_ID</li>
                                    <li>• https://youtube.com/watch?v=VIDEO_ID</li>
                                </ul>
                            </div>

                            <button onclick="extractThumbnails()" class="w-full px-6 py-3 lg:py-4 text-white font-bold text-base lg:text-lg rounded-lg lg:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" style="background: linear-gradient(45deg, #EF4444, #DC2626);">
                                <span class="flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span>Extract Thumbnails</span>
                                </span>
                            </button>
                        </div>

                        <!-- Results Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Available Thumbnails:</label>
                                <div id="thumbnailsContainer" class="w-full min-h-[400px] px-3 lg:px-4 py-3 lg:py-4 border-2 rounded-lg lg:rounded-xl overflow-y-auto" style="border-color: #FECACA; background: linear-gradient(45deg, #FEF2F2, #FECACA);">
                                    <div class="text-center text-gray-500 py-8">
                                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <p class="text-lg font-medium">Enter YouTube URL to extract thumbnails</p>
                                        <p class="text-sm mt-2">All available resolutions will be displayed here</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-4" style="background: linear-gradient(45deg, #EF4444, #EC4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Why Use Our Thumbnail Downloader?</h2>
                    <p class="text-gray-600 text-lg max-w-3xl mx-auto">Get high-quality YouTube thumbnails for your projects, presentations, or content creation</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6 bg-red-50 rounded-xl">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Multiple Resolutions</h3>
                        <p class="text-gray-600">Download thumbnails in various sizes from 120x90 to 1280x720 pixels</p>
                    </div>

                    <div class="text-center p-6 bg-green-50 rounded-xl">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Instant Download</h3>
                        <p class="text-gray-600">Get thumbnails instantly without any processing delays or waiting time</p>
                    </div>

                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">100% Free</h3>
                        <p class="text-gray-600">No registration required. Download unlimited thumbnails completely free</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="py-12 lg:py-16 bg-gray-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-4 text-gray-800">How It Works</h2>
                    <p class="text-gray-600 text-lg">Simple 3-step process to download YouTube thumbnails</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">1</div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Paste YouTube URL</h3>
                        <p class="text-gray-600">Copy and paste the YouTube video URL into the input field above</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">2</div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Extract Thumbnails</h3>
                        <p class="text-gray-600">Click the extract button to generate all available thumbnail sizes</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">3</div>
                        <h3 class="text-lg font-bold mb-2 text-gray-800">Download</h3>
                        <p class="text-gray-600">Choose your preferred resolution and download the thumbnail image</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/youtube-thumbnail-downloader.js"></script>
</body>
</html>
