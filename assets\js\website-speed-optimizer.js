// Website Speed Optimizer Functionality

function analyzeSpeed() {
    const websiteUrl = document.getElementById('websiteUrl').value.trim();
    
    if (!websiteUrl) {
        showError('Please enter a website URL');
        return;
    }
    
    // Validate URL format
    try {
        new URL(websiteUrl);
    } catch (e) {
        showError('Please enter a valid URL (e.g., https://example.com)');
        return;
    }
    
    // Show loading state
    showLoading();
    
    // Simulate speed analysis
    performSpeedAnalysis(websiteUrl);
}

function performSpeedAnalysis(url) {
    const steps = [
        'Connecting to website...',
        'Measuring loading times...',
        'Analyzing Core Web Vitals...',
        'Checking resource optimization...',
        'Generating recommendations...'
    ];
    
    let currentStep = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(() => {
        currentStep++;
        const progress = (currentStep / steps.length) * 100;
        
        progressBar.style.width = `${progress}%`;
        progressText.textContent = `${Math.round(progress)}%`;
        
        if (currentStep >= steps.length) {
            clearInterval(interval);
            // Simulate final analysis
            setTimeout(() => {
                const results = generateSpeedResults(url);
                displayResults(results);
            }, 1000);
        }
    }, 1200);
}

function generateSpeedResults(url) {
    // Simulate realistic speed test results
    const baseScore = 60 + Math.random() * 35; // 60-95 range
    
    return {
        url: url,
        performanceScore: Math.round(baseScore),
        coreWebVitals: {
            lcp: {
                value: (1.5 + Math.random() * 2).toFixed(1), // 1.5-3.5s
                rating: baseScore > 80 ? 'good' : baseScore > 60 ? 'needs-improvement' : 'poor'
            },
            fid: {
                value: (50 + Math.random() * 200).toFixed(0), // 50-250ms
                rating: baseScore > 80 ? 'good' : baseScore > 60 ? 'needs-improvement' : 'poor'
            },
            cls: {
                value: (0.05 + Math.random() * 0.2).toFixed(3), // 0.05-0.25
                rating: baseScore > 80 ? 'good' : baseScore > 60 ? 'needs-improvement' : 'poor'
            }
        },
        metrics: {
            firstContentfulPaint: (1.2 + Math.random() * 1.8).toFixed(1),
            speedIndex: (2.1 + Math.random() * 2.4).toFixed(1),
            timeToInteractive: (3.2 + Math.random() * 3.8).toFixed(1),
            totalBlockingTime: (100 + Math.random() * 400).toFixed(0),
            cumulativeLayoutShift: (0.05 + Math.random() * 0.2).toFixed(3)
        },
        recommendations: generateRecommendations(baseScore)
    };
}

function generateRecommendations(score) {
    const allRecommendations = [
        {
            title: 'Optimize Images',
            description: 'Compress and resize images to reduce file sizes. Use modern formats like WebP.',
            impact: 'High',
            effort: 'Medium',
            savings: '1.2s'
        },
        {
            title: 'Minify CSS and JavaScript',
            description: 'Remove unnecessary characters from CSS and JS files to reduce file sizes.',
            impact: 'Medium',
            effort: 'Low',
            savings: '0.8s'
        },
        {
            title: 'Enable Browser Caching',
            description: 'Set appropriate cache headers to store resources in user browsers.',
            impact: 'High',
            effort: 'Low',
            savings: '2.1s'
        },
        {
            title: 'Use a Content Delivery Network (CDN)',
            description: 'Distribute content globally to reduce server response times.',
            impact: 'High',
            effort: 'Medium',
            savings: '1.5s'
        },
        {
            title: 'Reduce Server Response Time',
            description: 'Optimize database queries and server configuration.',
            impact: 'Medium',
            effort: 'High',
            savings: '0.9s'
        },
        {
            title: 'Eliminate Render-Blocking Resources',
            description: 'Defer or async load CSS and JavaScript that blocks page rendering.',
            impact: 'High',
            effort: 'Medium',
            savings: '1.7s'
        },
        {
            title: 'Optimize Web Fonts',
            description: 'Use font-display: swap and preload critical fonts.',
            impact: 'Medium',
            effort: 'Low',
            savings: '0.6s'
        },
        {
            title: 'Reduce Unused JavaScript',
            description: 'Remove or defer JavaScript that is not needed for initial page load.',
            impact: 'Medium',
            effort: 'Medium',
            savings: '1.1s'
        }
    ];
    
    // Return 4-6 recommendations based on score
    const numRecommendations = score < 70 ? 6 : score < 85 ? 4 : 3;
    return allRecommendations.slice(0, numRecommendations);
}

function displayResults(results) {
    // Hide loading and initial states
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('initialState').classList.add('hidden');
    
    // Show results
    document.getElementById('resultsContainer').classList.remove('hidden');
    
    // Update URL
    document.getElementById('analyzedUrl').textContent = results.url;
    
    // Update performance score
    const performanceScore = document.getElementById('performanceScore');
    performanceScore.textContent = results.performanceScore;
    
    // Update score color based on value
    const scoreContainer = performanceScore.parentElement.parentElement;
    if (results.performanceScore >= 90) {
        scoreContainer.className = scoreContainer.className.replace('border-blue-200 bg-blue-50', 'border-green-200 bg-green-50');
        performanceScore.className = performanceScore.className.replace('text-blue-600', 'text-green-600');
        performanceScore.nextElementSibling.className = performanceScore.nextElementSibling.className.replace('text-blue-700', 'text-green-700');
    } else if (results.performanceScore < 70) {
        scoreContainer.className = scoreContainer.className.replace('border-blue-200 bg-blue-50', 'border-red-200 bg-red-50');
        performanceScore.className = performanceScore.className.replace('text-blue-600', 'text-red-600');
        performanceScore.nextElementSibling.className = performanceScore.nextElementSibling.className.replace('text-blue-700', 'text-red-700');
    }
    
    // Update Core Web Vitals
    updateCoreWebVital('lcpScore', results.coreWebVitals.lcp.value + 's', results.coreWebVitals.lcp.rating);
    updateCoreWebVital('fidScore', results.coreWebVitals.fid.value + 'ms', results.coreWebVitals.fid.rating);
    updateCoreWebVital('clsScore', results.coreWebVitals.cls.value, results.coreWebVitals.cls.rating);
    
    // Update detailed metrics
    displayDetailedMetrics(results.metrics);
    
    // Update recommendations
    displayRecommendations(results.recommendations);
}

function updateCoreWebVital(elementId, value, rating) {
    const element = document.getElementById(elementId);
    element.textContent = value;
    
    const container = element.closest('.bg-white');
    const icon = container.querySelector('.rounded-full');
    const iconSvg = icon.querySelector('svg');
    
    // Update colors based on rating
    if (rating === 'good') {
        icon.className = icon.className.replace(/bg-\w+-100/, 'bg-green-100');
        iconSvg.className = iconSvg.className.replace(/text-\w+-600/, 'text-green-600');
        element.className = element.className.replace(/text-\w+-600/, 'text-green-600');
    } else if (rating === 'needs-improvement') {
        icon.className = icon.className.replace(/bg-\w+-100/, 'bg-yellow-100');
        iconSvg.className = iconSvg.className.replace(/text-\w+-600/, 'text-yellow-600');
        element.className = element.className.replace(/text-\w+-600/, 'text-yellow-600');
    } else {
        icon.className = icon.className.replace(/bg-\w+-100/, 'bg-red-100');
        iconSvg.className = iconSvg.className.replace(/text-\w+-600/, 'text-red-600');
        element.className = element.className.replace(/text-\w+-600/, 'text-red-600');
    }
}

function displayDetailedMetrics(metrics) {
    const container = document.getElementById('detailedMetrics');
    
    const metricsData = [
        { name: 'First Contentful Paint', value: metrics.firstContentfulPaint + 's', description: 'Time until first text or image is painted' },
        { name: 'Speed Index', value: metrics.speedIndex + 's', description: 'How quickly content is visually displayed' },
        { name: 'Time to Interactive', value: metrics.timeToInteractive + 's', description: 'Time until page is fully interactive' },
        { name: 'Total Blocking Time', value: metrics.totalBlockingTime + 'ms', description: 'Time between FCP and TTI' },
        { name: 'Cumulative Layout Shift', value: metrics.cumulativeLayoutShift, description: 'Measure of visual stability' }
    ];
    
    let html = '';
    metricsData.forEach(metric => {
        html += `
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex justify-between items-start mb-2">
                    <h4 class="font-semibold text-gray-800">${metric.name}</h4>
                    <span class="text-lg font-bold text-blue-600">${metric.value}</span>
                </div>
                <p class="text-sm text-gray-600">${metric.description}</p>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function displayRecommendations(recommendations) {
    const container = document.getElementById('recommendations');
    
    let html = '';
    recommendations.forEach((rec, index) => {
        const impactColor = rec.impact === 'High' ? 'text-red-600 bg-red-100' : 
                           rec.impact === 'Medium' ? 'text-yellow-600 bg-yellow-100' : 
                           'text-green-600 bg-green-100';
        
        const effortColor = rec.effort === 'High' ? 'text-red-600 bg-red-100' : 
                           rec.effort === 'Medium' ? 'text-yellow-600 bg-yellow-100' : 
                           'text-green-600 bg-green-100';
        
        html += `
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="text-lg font-semibold text-gray-800">${rec.title}</h4>
                    <span class="text-sm font-bold text-blue-600">Save ${rec.savings}</span>
                </div>
                <p class="text-gray-600 mb-3">${rec.description}</p>
                <div class="flex space-x-4">
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${impactColor}">
                        ${rec.impact} Impact
                    </span>
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${effortColor}">
                        ${rec.effort} Effort
                    </span>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function showLoading() {
    document.getElementById('initialState').classList.add('hidden');
    document.getElementById('resultsContainer').classList.add('hidden');
    document.getElementById('loadingState').classList.remove('hidden');
    
    // Reset progress
    document.getElementById('progressBar').style.width = '0%';
    document.getElementById('progressText').textContent = '0%';
}

function showError(message) {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('resultsContainer').classList.add('hidden');
    
    const initialState = document.getElementById('initialState');
    initialState.classList.remove('hidden');
    initialState.innerHTML = `
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-bold text-red-700 mb-2">Error</h3>
        <p class="text-red-600 max-w-2xl mx-auto">${escapeHtml(message)}</p>
        <button onclick="resetTool()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Try Again
        </button>
    `;
}

function resetTool() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('resultsContainer').classList.add('hidden');
    
    const initialState = document.getElementById('initialState');
    initialState.classList.remove('hidden');
    initialState.innerHTML = `
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-800 mb-2">Ready to Test Speed</h3>
        <p class="text-gray-600 max-w-2xl mx-auto">Enter your website URL above to get a comprehensive speed analysis with Core Web Vitals and optimization recommendations.</p>
    `;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for URL input
    document.getElementById('websiteUrl').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            analyzeSpeed();
        }
    });
});
