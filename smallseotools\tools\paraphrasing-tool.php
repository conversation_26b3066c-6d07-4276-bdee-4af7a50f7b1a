<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paraphrasing Tool - Free Online Text Rewriter</title>
    <meta name="description" content="Rewrite and paraphrase your text with our free online paraphrasing tool. Create unique content while maintaining the original meaning.">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 10px 0;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
        }
        
        .header-links {
            display: flex;
            gap: 20px;
        }
        
        .header-links a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        
        .header-links a:hover {
            color: #007bff;
        }
        
        /* Main Content */
        .main-content {
            background: #fff;
            margin: 20px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tool-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .text-areas {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 250px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            outline: none;
            font-family: inherit;
        }
        
        .form-textarea:focus {
            border-color: #007bff;
        }
        
        .output-textarea {
            background: #f8f9fa;
            border-color: #28a745;
        }
        
        .mode-selector {
            margin-bottom: 20px;
        }
        
        .mode-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .mode-option {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .mode-option input[type="radio"] {
            margin: 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
            display: none;
        }
        
        .char-count {
            text-align: right;
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 10px;
            }
            
            .main-content {
                margin: 10px 0;
                padding: 20px;
            }
            
            .tool-title {
                font-size: 24px;
            }
            
            .text-areas {
                grid-template-columns: 1fr;
            }
            
            .mode-options {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <a href="../index.php" class="logo">Small SEO Tools</a>
                <div class="header-links">
                    <a href="../login.php">Login</a>
                    <a href="../pricing.php">Pricing</a>
                    <a href="../contact.php">Contact</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <h1 class="tool-title">Paraphrasing Tool</h1>
            <p class="tool-description">
                Rewrite your text while maintaining the original meaning. Our paraphrasing tool helps you create unique content 
                by rephrasing sentences and replacing words with synonyms.
            </p>
            
            <!-- Mode Selector -->
            <div class="mode-selector">
                <label class="form-label">Paraphrasing Mode:</label>
                <div class="mode-options">
                    <div class="mode-option">
                        <input type="radio" id="standard" name="mode" value="standard" checked>
                        <label for="standard">Standard</label>
                    </div>
                    <div class="mode-option">
                        <input type="radio" id="fluency" name="mode" value="fluency">
                        <label for="fluency">Fluency</label>
                    </div>
                    <div class="mode-option">
                        <input type="radio" id="creative" name="mode" value="creative">
                        <label for="creative">Creative</label>
                    </div>
                    <div class="mode-option">
                        <input type="radio" id="formal" name="mode" value="formal">
                        <label for="formal">Formal</label>
                    </div>
                </div>
            </div>
            
            <!-- Text Areas -->
            <div class="text-areas">
                <div class="form-group">
                    <label for="inputText" class="form-label">Original Text:</label>
                    <textarea 
                        id="inputText" 
                        class="form-textarea" 
                        placeholder="Enter your text here to paraphrase..."
                        maxlength="5000"
                    ></textarea>
                    <div class="char-count">
                        <span id="inputCharCount">0</span> / 5000 characters
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="outputText" class="form-label">Paraphrased Text:</label>
                    <textarea 
                        id="outputText" 
                        class="form-textarea output-textarea" 
                        placeholder="Paraphrased text will appear here..."
                        readonly
                    ></textarea>
                    <div class="char-count">
                        <span id="outputCharCount">0</span> characters
                    </div>
                </div>
            </div>
            
            <!-- Loading -->
            <div id="loadingDiv" class="loading">
                <p>Paraphrasing your text... Please wait.</p>
            </div>
            
            <!-- Buttons -->
            <div>
                <button class="btn" id="paraphraseBtn" onclick="paraphraseText()">Paraphrase Text</button>
                <button class="btn btn-secondary" onclick="clearText()">Clear All</button>
                <button class="btn btn-secondary" onclick="copyOutput()">Copy Result</button>
                <button class="btn btn-secondary" onclick="swapTexts()">Swap Texts</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Small SEO Tools. All rights reserved.</p>
        </div>
    </footer>

    <script>
        const inputText = document.getElementById('inputText');
        const outputText = document.getElementById('outputText');
        const inputCharCount = document.getElementById('inputCharCount');
        const outputCharCount = document.getElementById('outputCharCount');
        const paraphraseBtn = document.getElementById('paraphraseBtn');
        const loadingDiv = document.getElementById('loadingDiv');

        // Character counters
        inputText.addEventListener('input', function() {
            inputCharCount.textContent = this.value.length;
            paraphraseBtn.disabled = this.value.trim().length === 0;
        });

        function paraphraseText() {
            const text = inputText.value.trim();
            if (!text) {
                alert('Please enter some text to paraphrase.');
                return;
            }
            
            paraphraseBtn.disabled = true;
            loadingDiv.style.display = 'block';
            outputText.value = '';
            
            // Simulate paraphrasing process
            setTimeout(() => {
                const paraphrased = generateParaphrased(text);
                outputText.value = paraphrased;
                outputCharCount.textContent = paraphrased.length;
                loadingDiv.style.display = 'none';
                paraphraseBtn.disabled = false;
            }, 2000);
        }

        function generateParaphrased(text) {
            // Simple paraphrasing simulation
            const synonyms = {
                'good': ['excellent', 'great', 'fine', 'wonderful'],
                'bad': ['poor', 'terrible', 'awful', 'horrible'],
                'big': ['large', 'huge', 'enormous', 'massive'],
                'small': ['tiny', 'little', 'miniature', 'compact'],
                'fast': ['quick', 'rapid', 'swift', 'speedy'],
                'slow': ['sluggish', 'gradual', 'leisurely', 'unhurried'],
                'important': ['significant', 'crucial', 'vital', 'essential'],
                'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated'],
                'difficult': ['challenging', 'tough', 'hard', 'complex'],
                'beautiful': ['gorgeous', 'stunning', 'attractive', 'lovely'],
                'happy': ['joyful', 'cheerful', 'delighted', 'pleased'],
                'sad': ['unhappy', 'sorrowful', 'melancholy', 'dejected']
            };
            
            let paraphrased = text;
            
            // Replace words with synonyms
            Object.keys(synonyms).forEach(word => {
                const regex = new RegExp('\\b' + word + '\\b', 'gi');
                paraphrased = paraphrased.replace(regex, (match) => {
                    const synonymList = synonyms[word.toLowerCase()];
                    const randomSynonym = synonymList[Math.floor(Math.random() * synonymList.length)];
                    return match === match.toUpperCase() ? randomSynonym.toUpperCase() : 
                           match[0] === match[0].toUpperCase() ? randomSynonym.charAt(0).toUpperCase() + randomSynonym.slice(1) : 
                           randomSynonym;
                });
            });
            
            // Simple sentence restructuring
            const sentences = paraphrased.split(/[.!?]+/).filter(s => s.trim());
            const restructured = sentences.map(sentence => {
                sentence = sentence.trim();
                if (sentence.includes(' and ')) {
                    return sentence.replace(' and ', ' as well as ');
                }
                if (sentence.includes(' but ')) {
                    return sentence.replace(' but ', ' however, ');
                }
                if (sentence.includes(' because ')) {
                    return sentence.replace(' because ', ' since ');
                }
                return sentence;
            });
            
            return restructured.join('. ') + '.';
        }

        function clearText() {
            inputText.value = '';
            outputText.value = '';
            inputCharCount.textContent = '0';
            outputCharCount.textContent = '0';
            paraphraseBtn.disabled = true;
        }

        function copyOutput() {
            if (!outputText.value) {
                alert('No paraphrased text to copy.');
                return;
            }
            outputText.select();
            document.execCommand('copy');
            alert('Paraphrased text copied to clipboard!');
        }

        function swapTexts() {
            if (!outputText.value) {
                alert('No paraphrased text to swap.');
                return;
            }
            const temp = inputText.value;
            inputText.value = outputText.value;
            outputText.value = temp;
            
            inputCharCount.textContent = inputText.value.length;
            outputCharCount.textContent = outputText.value.length;
            paraphraseBtn.disabled = inputText.value.trim().length === 0;
        }

        // Initialize
        paraphraseBtn.disabled = true;
    </script>
</body>
</html>
